import { useState } from "react";

import { Divider, Grid, Stack, Typography } from "@mui/material";
import Item from "@mui/material/Stack";

import DeliveryDetailsModal from "./deliveryDetailsModal";
import DeliveryDetailsMapModal from "./deliveryDetailsMapModal";

import {
  deliveryAddress,
  deliveryAddressGrid,
  deliveryAddressStack,
  editActionStyle,
  enterAddressStyles,
  labelStyles,
} from "checkout/checkoutForm/index.styles";
import { inputLabelStyle } from "styles";
import { borderColor, pkCurrencyCode, UIElementIds } from "utils/constant";
import { getLabelColor } from "utils/helperFunctions";
import { useSelector } from "store";

const DeliveryDetails = () => {
  const [toggleDeliveryModal, setToggleDeliveryModal] =
    useState<boolean>(false);

  const { address, validationError, disableAddressUpdate } = useSelector(
    (state) => state.checkout
  );
  const { businessDetails } = useSelector((state) => state.business);

  // ==============================|| handler functions ||============================== //

  // function to open/close the delivery details modal
  const handleToggleDeliveryModal = () => {
    setToggleDeliveryModal((prevState) => !prevState);
  };

  // ==============================|| UI ||============================== //

  // funCtion to return delivery address modal UI
  const getDeliveryModelUI = () => {
    return businessDetails?.currencyCode === pkCurrencyCode ? (
      <DeliveryDetailsMapModal
        toggleDeliveryModal={toggleDeliveryModal}
        handleToggleDeliveryModal={handleToggleDeliveryModal}
      />
    ) : (
      <DeliveryDetailsModal
        toggleDeliveryModal={toggleDeliveryModal}
        handleToggleDeliveryModal={handleToggleDeliveryModal}
      />
    );
  };

  // function to return either delivery address or address input field
  const toggleDeliveryAddressUI = () => {
    return address ? (
      <Stack spacing={2}>
        <Stack sx={deliveryAddressStack}>
          <Typography
            id={UIElementIds?.deliveryAddress}
            component="small"
            sx={{
              ...labelStyles,
              color: getLabelColor(validationError.deliveryAddress),
            }}
          >
            Your address*
          </Typography>

          {/* restrict address update according to business flag */}
          {!disableAddressUpdate && (
            <Typography
              component="strong"
              onClick={handleToggleDeliveryModal}
              sx={editActionStyle}
            >
              Edit
            </Typography>
          )}
        </Stack>

        <Item component="p" sx={deliveryAddress}>
          {address}
        </Item>
      </Stack>
    ) : (
      <Grid container sx={deliveryAddressGrid}>
        <Grid item>
          <Typography
            id={UIElementIds?.deliveryAddress}
            component="small"
            sx={{
              ...inputLabelStyle,
              color: getLabelColor(validationError.deliveryAddress),
            }}
          >
            Your address*
          </Typography>

          <Typography
            variant="button"
            component="button"
            sx={enterAddressStyles}
            onClick={handleToggleDeliveryModal}
          >
            Enter your address
          </Typography>
        </Grid>
      </Grid>
    );
  };

  return (
    <>
      {/* toggle the address UI between map & without map modal */}
      {toggleDeliveryModal && <>{getDeliveryModelUI()}</>}

      <Divider sx={{ borderColor }} />

      {/* toggle the UI based on user address (added or not) */}
      {toggleDeliveryAddressUI()}
    </>
  );
};

export default DeliveryDetails;
