import { useCallback } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { paymentMethods } from "utils/constant";
import { paymentMethodFailureMessage } from "utils/helperFunctions";
import { onlinePayment } from "utils/apiConfig";
import { dispatch, useSelector } from "store";
import { bulkUpdateCheckoutStore } from "store/slices/checkout";

export const usePayMobPayment = () => {
  const { businessId, paymentDetails, checkoutProcessState } = useSelector(
    (state) => state.checkout
  );

  // ==============================|| API ||============================== //

  // API call to get payMob token
  const [{ data, loading, error }, getPayMobTokenAPICall] = useAxios(
    onlinePayment(businessId, paymentMethods?.PAY_MOB?.value),
    { manual: true }
  );

  // ==============================|| handler functions ||============================== //

  // function to get payMob payment token
  const createPayMobToken = useCallback(
    async (config: AxiosRequestConfig, orderId: number): Promise<void> => {
      try {
        // API call to get payMob token
        const { data } = await getPayMobTokenAPICall(config);

        // verify API response
        if (data?.status === 200 && data?.result?.token) {
          // set token & order id in store to load payment method
          dispatch(
            bulkUpdateCheckoutStore({
              type: "",
              value: {
                checkoutProcessState: { ...checkoutProcessState, id: orderId },
                paymentDetails: {
                  ...paymentDetails,
                  payMobToken: data?.result?.token,
                },
              },
            })
          );
        } else {
          // set the payment method failure message for user
          paymentMethodFailureMessage(orderId);
        }
      } catch (error) {
        // set the payment method failure message for user
        paymentMethodFailureMessage(orderId);
      }
    },

    // eslint-disable-next-line react-hooks/exhaustive-deps
    [getPayMobTokenAPICall]
  );

  return {
    data,
    loading,
    error,
    getPayMobTokenAPICall: createPayMobToken,
  };
};
