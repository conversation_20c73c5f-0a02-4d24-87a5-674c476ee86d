import { useEffect } from "react";

import { Box } from "@mui/material";

import PageError from "./pageError";
import CheckoutForm from "./checkoutForm";
import ConfirmationPage from "./confirmationPage";

import { UserSavedData } from "./index.types";
import { initialOrderSchedule, subscriptionTypes } from "utils/constant";
import {
  capitalizeFirstLetter,
  getCookies,
  getSiteUrl,
  preCheckoutFailureMessage,
  validateBusinessInfo,
} from "utils/helperFunctions";
import { dispatch, useSelector } from "store";
import {
  bulkUpdateCheckoutStore,
  updateCheckoutStore,
} from "store/slices/checkout";

const Checkout = () => {
  const { cartId, subscription, checkoutProcessState } = useSelector(
    (state) => state.checkout
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function call to get details saved in cookies
    retrieveCookiesData();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // ==============================|| handler functions ||============================== //

  // function to get user details saved in cookies
  const retrieveCookiesData = (): void => {
    // function to retrieve saved cookies
    const userDetails: UserSavedData | {} = getCookies() || {};

    /**
     * function call to access the data sent from site
     * calling here to get the delivery charges on stored location
     */
    getPreCheckoutData(userDetails as UserSavedData);
  };

  // function to access the data sent from cross technology
  const getPreCheckoutData = (userDetails: UserSavedData): void => {
    // validate retrieved data before proceeding to checkout form component
    if (
      typeof businessInfo !== "undefined" &&
      validateBusinessInfo(businessInfo)
    ) {
      // verify if subscription is enabled
      const isSubscriptionEnabled: boolean = Object.values(
        subscriptionTypes
      )?.includes(businessInfo?.subscriptionType);

      // set data in store
      dispatch(
        bulkUpdateCheckoutStore({
          type: "",
          value: {
            ...userDetails,

            paymentDetails: {
              ...userDetails?.paymentDetails,
              stripe: {
                ...userDetails?.paymentDetails?.stripe,
                saveCard:
                  isSubscriptionEnabled ||
                  userDetails?.paymentDetails?.stripe?.saveCard,
              },
            },

            cartId: businessInfo?.cartId,
            businessId: businessInfo?.businessId,
            branchId: businessInfo?.branchId,
            orderType: businessInfo?.orderType || "",
            siteUrl: getSiteUrl(),
            source: businessInfo?.source,
            orderSchedule: businessInfo?.orderSchedule || initialOrderSchedule,
            subscription: {
              ...subscription,
              type: isSubscriptionEnabled ? businessInfo?.subscriptionType : "",
            },

            userLocation: {
              lat: userDetails?.userLocation?.lat
                ? userDetails?.userLocation?.lat
                : Number(businessInfo?.userLocation?.lat) || 0,
              lng: userDetails?.userLocation?.lng
                ? userDetails?.userLocation?.lng
                : Number(businessInfo?.userLocation?.lng) || 0,
            },

            // user address details if got from cross platform
            address:
              capitalizeFirstLetter(
                businessInfo?.addressDetails?.address?.trim()
              ) ||
              userDetails?.address ||
              "",
            apartment:
              capitalizeFirstLetter(
                businessInfo?.addressDetails?.apartment?.trim()
              ) ||
              userDetails?.apartment ||
              "",
            userArea:
              capitalizeFirstLetter(
                businessInfo?.addressDetails?.area?.trim()
              ) ||
              userDetails?.userArea ||
              "",
            userCity:
              capitalizeFirstLetter(
                businessInfo?.addressDetails?.city?.trim()
              ) ||
              userDetails?.userCity ||
              "",

            userPostalCode:
              businessInfo?.addressDetails?.postalCode ||
              userDetails?.userPostalCode ||
              "",
            userCountry:
              businessInfo?.addressDetails?.country ||
              userDetails?.userCountry ||
              "",

            theme: businessInfo?.theme
              ? {
                  headerBackground: businessInfo?.theme?.header_bg || "",
                  headerFontColor: businessInfo?.theme?.header_font_color || "",
                  bodyBackground: businessInfo?.theme?.body_bg || "",
                  bodyFontColor: businessInfo?.theme?.body_font_color || "",
                  buttonBackground: businessInfo?.theme?.button_bg || "",
                  buttonFontColor: businessInfo?.theme?.button_font_color || "",
                  buttonRadius: businessInfo?.theme?.button_radius || "",
                }
              : null,

            disableAddressUpdate: businessInfo?.disableAddressUpdate || false,
            confirmationRedirectURL:
              businessInfo?.confirmationRedirectURL || "",
          },
        })
      );
    } else {
      // set siteUrl in store
      dispatch(updateCheckoutStore({ type: "siteUrl", value: getSiteUrl() }));

      // function to set error page if checkout pre-requisite is missing
      preCheckoutFailureMessage();
    }
  };

  // ==============================|| UI ||============================== //

  // function to return the respective UI component
  const renderCheckoutContent = (): JSX.Element => {
    // render error page if there is an error in the checkout process
    if (checkoutProcessState?.errorFlag) {
      return <PageError />;
    }

    // render confirmation page if the checkout process is successful
    if (checkoutProcessState?.successFlag) {
      return <ConfirmationPage />;
    }

    // render checkout form if cartId is available
    if (cartId) {
      return <CheckoutForm />;
    }

    // render dummy component if none of the above conditions are met to prevent page jerk
    return <Box sx={{ height: "100vh" }}></Box>;
  };

  return <>{renderCheckoutContent()}</>;
};

export default Checkout;
