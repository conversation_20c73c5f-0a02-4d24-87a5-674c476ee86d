import { useEffect } from "react";

import {
  getAlfalahScriptAttributes,
  paymentMethodFailureMessage,
} from "utils/helperFunctions";
import { useSelector } from "store";

const BankAlfalah = () => {
  const { siteUrl, paymentDetails, checkoutProcessState } = useSelector(
    (state) => state.checkout
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // verify alfalah session id
    if (paymentDetails?.alfalahSessionId) {
      // function call to load bank alfalah script
      loadAlfalahScript();
    }

    // clean up function to remove alfalah script
    return () => {
      const alfalahScript: HTMLElement | null =
        document.getElementById("alfalahScript");

      if (alfalahScript) {
        alfalahScript.remove();
      }
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [paymentDetails?.alfalahSessionId]);

  // ==============================|| handler functions ||============================== //

  // function call to load bank alfalah script
  const loadAlfalahScript = (): void => {
    // load bank alfalah checkout script
    const script: HTMLScriptElement = document.createElement("script");

    script.src = `${process.env.REACT_APP_ALFALAH_SCRIPT}`;
    script.id = "alfalahScript";
    script.setAttribute(
      "data-error",
      getAlfalahScriptAttributes("error", siteUrl, checkoutProcessState?.id)
    );
    script.setAttribute(
      "data-cancel",
      getAlfalahScriptAttributes("cancel", siteUrl, checkoutProcessState?.id)
    );

    script.onload = () => {
      // once the script is loaded, configure and show the payment page
      window.Checkout.configure({
        session: { id: paymentDetails?.alfalahSessionId },
      });

      // Checkout => is defined in global.d.ts file (bank Alfalah provided functions)
      window.Checkout.showPaymentPage();
    };

    script.onerror = () => {
      // set message for failed to load payment method
      paymentMethodFailureMessage(checkoutProcessState?.id);

      // TODO: set sentry log here
    };

    // append script in DOM
    document.body.appendChild(script);
  };

  // ==============================|| UI ||============================== //

  return <></>;
};

export default BankAlfalah;
