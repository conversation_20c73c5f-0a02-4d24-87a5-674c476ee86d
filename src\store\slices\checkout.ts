import { createSlice } from "@reduxjs/toolkit";
import {
  SubscriptionType,
  PaymentMethodType,
  PaymentDetailsType,
  CheckoutProcessType,
} from "checkout/index.types";
import {
  CartDetailsType,
  LocationCoordinates,
  SiteThemeType,
  OrderScheduleType,
} from "types";
import { initialOrderSchedule, initialPaymentMethod } from "utils/constant";

export interface CheckoutTypes {
  // details from site
  cartId: string;
  businessId: string;
  branchId: string;
  siteUrl: string;
  source: string;

  // user details
  name: string;
  email: string;
  phone: string;
  userId: string;
  tdUserId: string;
  authToken: string;
  renewAuthToken: string;

  // delivery details
  address: string;
  streetAddress: string;
  apartment: string;

  // cart details
  cartDetails: CartDetailsType;

  // rest of helping details
  orderTypeList: string[];
  orderType: string;
  userPostalCode: string;
  userArea: string;
  userCountry: string;
  userCity: string;
  userCountryCode: string;
  userState: string;
  userLocation: LocationCoordinates;
  specialInstructions: string;
  couponCode: string;
  theme: null | SiteThemeType;
  orderSchedule: OrderScheduleType;

  /**
   * '0' => COD
   * '2' => bank transfer
   * '3' => other payment methods
   */
  paymentType: PaymentMethodType;
  paymentDetails: PaymentDetailsType;

  subscription: SubscriptionType;

  validationError: {
    name: boolean;
    email: boolean;
    phone: boolean;
    streetAddress: boolean;
    userArea: boolean;
    userCity: boolean;
    userPostalCode: boolean;
    deliveryAddress: boolean;
    orderSchedule: boolean;
    subscription: boolean;
    coupon: string;

    [key: string]: boolean | string;
  };

  checkoutProcessState: CheckoutProcessType;

  // using to prevent place order in case of delivery until user add delivery details
  userLocationFlag: boolean;
  disableComponentFlag: boolean;
  disableAddressUpdate: boolean;
  confirmationRedirectURL: string;

  apiCallError: boolean;

  [key: string]: any;
}

// initial state
const initialState: CheckoutTypes = {
  // details from site
  cartId: "",
  businessId: "",
  branchId: "",
  source: "",
  siteUrl: "",

  // user details
  name: "",
  email: "",
  phone: "",
  userId: "",
  tdUserId: "",
  authToken: "",
  renewAuthToken: "",

  // delivery details
  address: "",
  streetAddress: "",
  apartment: "",

  // order schedule
  orderSchedule: initialOrderSchedule,

  // cart details
  cartDetails: {
    orderid: 0,
    temp_order_id: "",
    total: 0,
    tax: 0,
    tax_value: 0,
    discount: 0,
    discount_value: 0,
    gtotal: 0,
    status: 0,
    ordertype: "",
    delivery_charges: 0,
    delivery_tax: 0,
    delivery_tax_value: 0,
    items: [],

    coupon_discount: 0,
    coupon_discount_value: 0,
    tip: 0,
  },

  // other details
  orderTypeList: [],
  orderType: "",
  userPostalCode: "",
  userArea: "",
  userCountry: "",
  userCity: "",
  userCountryCode: "",
  userState: "",
  userLocation: { lat: 0, lng: 0 },
  specialInstructions: "",
  couponCode: "",
  theme: null,

  /**
   * '0' => COD
   * '2' => bank transfer
   * '3' => other payment methods
   */
  paymentType: initialPaymentMethod,
  paymentDetails: {
    stripe: { stripeCustomerId: "", selectedCardId: "", saveCard: false },
    alfalahSessionId: "",
    payMobToken: "",
    payFast: {
      merchantId: "",
      merchantName: "",
      storeId: "",
      invoiceId: "",
      token: "",
    },
  },

  subscription: {
    type: "",
    time: "",
    details: {},
  },

  validationError: {
    name: false,
    email: false,
    phone: false,
    streetAddress: false,
    userArea: false,
    userCity: false,
    userPostalCode: false,
    deliveryAddress: false,
    orderSchedule: false,
    subscription: false,
    coupon: "",
  },

  checkoutProcessState: {
    id: 0,
    successFlag: false,
    errorFlag: false,
    errorCode: 404,
    statusMessage: "",
    message: "",
    reloadPaymentMethod: false,
  },

  /**
   * using this key to prevent post order
   * case => user did not add address
   * case => user returned after selecting a new branch
   * initially false => validation disabled
   */
  userLocationFlag: false,
  disableComponentFlag: false,
  disableAddressUpdate: false,
  confirmationRedirectURL: "",

  apiCallError: false,
};

const Checkout = createSlice({
  name: "checkout",
  initialState,
  reducers: {
    updateCheckoutStore(state, action) {
      state[action.payload.type] = action.payload.value;
    },
    bulkUpdateCheckoutStore(state, action) {
      return { ...state, ...action.payload.value };
    },
  },
});

export default Checkout.reducer;

export const { updateCheckoutStore, bulkUpdateCheckoutStore } =
  Checkout.actions;
