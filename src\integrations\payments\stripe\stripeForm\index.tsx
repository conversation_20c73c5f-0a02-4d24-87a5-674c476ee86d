import {
  FormControlLabel,
  FormGroup,
  Grid,
  Stack,
  Typography,
  Box,
  Checkbox,
  CardMedia,
} from "@mui/material";
import {
  CardCvcElement,
  CardExpiryElement,
  CardNumberElement,
  useElements,
  useStripe,
} from "@stripe/react-stripe-js";
import { Stripe, StripeElements } from "@stripe/stripe-js";

import {
  labelStyles,
  paymentCardsIcons,
  saveCardChkBox,
  saveCardLabel,
  stripeField,
  stripeForm,
  stripeFormStyles,
  tabContentTypoStyle,
} from "checkout/checkoutForm/index.styles";
import { justifySpaceBetween } from "styles";
import { cardIconsPath } from "utils/constant";
import { dispatch, useSelector } from "store";
import { updateCheckoutStore } from "store/slices/checkout";

/**
 * exporting these variables to placeOrder component
 * exporting from here because it is required to be wrapped in the stripe Elements component
 */
export let stripe: Stripe | null = null;
export let elements: StripeElements | null = null;

const StripeForm = () => {
  const { paymentDetails, subscription, userId } = useSelector(
    (state) => state.checkout
  );

  // load stripe SDK & card elements
  stripe = useStripe();
  elements = useElements();

  // ==============================|| handler functions ||============================== //

  // save card checkbox handler
  const handleChange = (event: { target: { checked: boolean } }): void => {
    // set data in store
    dispatch(
      updateCheckoutStore({
        type: "paymentDetails",
        value: {
          ...paymentDetails,
          stripe: {
            ...paymentDetails?.stripe,
            saveCard: event.target.checked,
          },
        },
      })
    );
  };

  // ==============================|| UI ||============================== //

  return (
    <Box sx={stripeFormStyles}>
      <Box sx={{ ...justifySpaceBetween, mb: "20px" }}>
        <Stack direction="row" spacing={{ xs: 0.4, sm: 1.75 }}>
          {Object.values(cardIconsPath)?.map((imagePath) => (
            <CardMedia
              key={imagePath}
              component="img"
              image={imagePath}
              alt={"payment"}
              sx={paymentCardsIcons}
            />
          ))}
        </Stack>

        {/* allow save card only for logged in users */}
        {userId && (
          <FormGroup>
            <FormControlLabel
              label="Save card"
              control={
                <Checkbox
                  checked={paymentDetails?.stripe?.saveCard}
                  size="large"
                  disabled={!!subscription?.type}
                  onChange={handleChange}
                  sx={saveCardChkBox}
                />
              }
              sx={saveCardLabel}
            />
          </FormGroup>
        )}
      </Box>

      <Grid container spacing={{ xs: 1.25, sm: 2.5 }} alignItems="center">
        {/* Card Number Field */}
        <Grid item xs={12} sm={6}>
          <Typography sx={labelStyles}>Card number</Typography>
          <Typography sx={tabContentTypoStyle}>
            Enter the 16–digit card number on the card
          </Typography>
        </Grid>

        <Grid item xs={12} sm={6}>
          <Stack sx={stripeField}>
            <CardNumberElement options={stripeForm} />
          </Stack>
        </Grid>

        {/* Expiry Date and CVV Fields */}
        <Grid item xs={12} sm={6}>
          <Typography sx={labelStyles}>Expiry date</Typography>
          <Typography sx={tabContentTypoStyle}>
            Enter the expiration date of the card
          </Typography>
        </Grid>

        <Grid item xs={12} sm={6}>
          <Grid container spacing={{ xs: 1.25, sm: 2 }} alignItems="center">
            <Grid item xs={12} sm={4.25}>
              <Stack sx={stripeField}>
                <CardExpiryElement options={stripeForm} />
              </Stack>
            </Grid>

            <Grid item xs={12} sm={3.5}>
              <Typography sx={labelStyles}>CVV</Typography>
              <Typography sx={tabContentTypoStyle}>Security code</Typography>
            </Grid>

            <Grid item xs={12} sm={4.25}>
              <Stack sx={stripeField}>
                <CardCvcElement options={stripeForm} />
              </Stack>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default StripeForm;
