import { useRef, useState } from "react";

import { Stack, Typography, Paper, Box } from "@mui/material";
import Item from "@mui/material/Stack";
import { Login } from "@mui/icons-material";

import Button from "components/button";
import ManualAuth from "./manualAuth";
import SocialAuth from "./socialAuth";

import {
  createAccount,
  authIconStyles,
  authParentStack,
  emailButtonStyles,
  socialButtonsBox,
  socialLoginsStack,
} from "./index.styles";
import { paperStyles } from "checkout/checkoutForm/index.styles";
import { socialPlatforms, userAuthTypes } from "utils/constant";
import { useSelector } from "store";

const ManageAccount = () => {
  const [toggleUserManualAuth, setToggleUserManualAuth] =
    useState<boolean>(false);

  const selectedAuthTab = useRef<string>(userAuthTypes?.LOGIN);

  const firebaseDetails = useSelector((state) => state.firebase);

  // ==============================|| handler functions ||============================== //

  // manual auth process handler
  const handleManualAuthProcess = (authTab: string): void => {
    // set the selected tab on modal
    selectedAuthTab.current = authTab;

    // open the modal
    handleToggleUserManualAuth();
  };

  // function to toggle the modal state
  const handleToggleUserManualAuth = (): void => {
    setToggleUserManualAuth((prevState) => !prevState);
  };

  // ==============================|| UI ||============================== //

  // function to return social login buttons UI based on Firebase configurations
  const getSocialLoginUI = () => {
    return (
      <>
        {firebaseDetails?.socialLogin?.google && (
          <SocialAuth socialPlatform={socialPlatforms?.GOOGLE} />
        )}

        {firebaseDetails?.socialLogin?.facebook && (
          <SocialAuth socialPlatform={socialPlatforms?.FACEBOOK} />
        )}
      </>
    );
  };

  return (
    <>
      {/* login & signUp modal */}
      {toggleUserManualAuth && (
        <ManualAuth
          toggleUserManualAuth={toggleUserManualAuth}
          handleToggleUserManualAuth={handleToggleUserManualAuth}
          authTab={selectedAuthTab.current}
        />
      )}

      <Paper elevation={0} sx={paperStyles}>
        <Stack spacing={2.5} sx={authParentStack}>
          <Item sx={{ width: "100%" }}>
            <Stack
              sx={socialLoginsStack}
              direction={{ xs: "column", md: "row" }}
              spacing={{ xs: 1.5, md: 0 }}
            >
              {/* social login buttons */}
              {getSocialLoginUI()}

              <Box sx={socialButtonsBox}>
                <Button
                  onClick={() => handleManualAuthProcess(userAuthTypes?.LOGIN)}
                  sx={emailButtonStyles}
                >
                  <Login sx={authIconStyles} />
                  Login to my account
                </Button>
              </Box>
            </Stack>
          </Item>

          <Typography
            onClick={() => handleManualAuthProcess(userAuthTypes?.SIGN_UP)}
            sx={createAccount}
          >
            Signup now
          </Typography>
        </Stack>
      </Paper>
    </>
  );
};

export default ManageAccount;
