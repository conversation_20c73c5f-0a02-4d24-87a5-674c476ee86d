import React, { useState, useRef, useCallback, useEffect } from "react";

import { Box, Tooltip } from "@mui/material";
import { Place, MyLocation } from "@mui/icons-material";
import { GoogleMap, useLoadScript } from "@react-google-maps/api";

import Skeleton from "components/skeleton";
import TextField from "components/textField";
import {
  locationIcon,
  mapContainer,
  mapContainerBox,
} from "checkout/checkoutForm/index.styles";
import { googleMapOptions, mapZoomLevel } from "utils/constant";
import { concatStrings, getCountryCode } from "utils/helperFunctions";
import { dispatch, useSelector } from "store";
import {
  bulkUpdateCheckoutStore,
  updateCheckoutStore,
} from "store/slices/checkout";

const GoogleMaps: React.FC = () => {
  const [searchInput, setSearchInput] = useState<string>("");

  const [error, setError] = useState<string>("");
  const [tooltipOpen, setTooltipOpen] = useState<boolean>(false);

  const { businessDetails, branchDetails } = useSelector(
    (state) => state.business
  );
  const { userLocation, streetAddress, userArea, userCity, validationError } =
    useSelector((state) => state.checkout);

  const mapRef = useRef<google.maps.Map>();

  // load the google map
  const { isLoaded } = useLoadScript({
    googleMapsApiKey: businessDetails.googleKey,

    // this enables the Places library without affecting Geocoding
    libraries: ["places"],
  });

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    if (isLoaded) {
      if (userLocation.lat && userLocation.lng && streetAddress) {
        setSearchInput(concatStrings([streetAddress, userArea, userCity]));
      } else {
        getCurrentLocation();
      }

      /**************************************************************************** */

      const autocomplete = new google.maps.places.Autocomplete(
        document.getElementById("searchAddress")! as HTMLInputElement,
        {
          // restrict results to a specific country
          componentRestrictions: {
            country: getCountryCode(branchDetails.country),
          },
        }
      );

      // debounced function to handle place change
      const handlePlaceChange = () => {
        const place = autocomplete.getPlace();

        if (!place?.geometry?.location) {
          return;
        }

        const location = place.geometry.location;

        mapRef.current?.panTo(location);

        let street = "";
        let area = "";
        let city = "";
        let country = "";
        let countryCode = "";
        let postalCode = "";

        place?.address_components?.forEach((component) => {
          if (
            component.types.includes("premise") ||
            component.types.includes("street_number") ||
            component.types.includes("route")
          ) {
            street = `${street}${street && component.long_name ? " " : ""}${
              component.long_name
            }`;
          }
          if (
            component.types.includes("sublocality") ||
            component.types.includes("neighborhood")
          ) {
            area = component.long_name;
          }
          if (component.types.includes("locality")) {
            city = component.long_name;
          }
          if (component.types.includes("country")) {
            country = component.long_name;
            countryCode = component.short_name;
          }
          if (component.types.includes("postal_code")) {
            postalCode = component.long_name;
          }
        });

        setSearchInput(concatStrings([street, area, city]));

        dispatch(
          bulkUpdateCheckoutStore({
            type: "",
            value: {
              userArea: area,
              userCountry: country,
              userCity: city,
              userCountryCode: countryCode,
              userPostalCode: postalCode,

              streetAddress: street,
              userLocation: location.toJSON(),

              validationError: {
                ...validationError,

                streetAddress: street ? "" : validationError.streetAddress,
                userArea: area ? "" : validationError.userArea,
                userCity: city ? "" : validationError.userCity,
              },
            },
          })
        );
      };

      autocomplete.addListener("place_changed", handlePlaceChange);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoaded]);

  // ==============================|| handler functions ||============================== //

  const handleTooltipClose = () => {
    setTooltipOpen(false);
  };

  const onLoad = useCallback((map: google.maps.Map) => {
    mapRef.current = map;
  }, []);

  const onDragEnd = useCallback(() => {
    const newCenter = mapRef.current?.getCenter()?.toJSON();

    if (newCenter) {
      dispatch(updateCheckoutStore({ type: "userLocation", value: newCenter }));

      reverseGeoCode(newCenter);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const reverseGeoCode = useCallback((coords: google.maps.LatLngLiteral) => {
    const geocoder = new google.maps.Geocoder();

    geocoder.geocode({ location: coords }, (results, status) => {
      if (status === "OK" && results?.[0]) {
        let street = "";
        let area = "";
        let city = "";
        let country = "";
        let countryCode = "";
        let postalCode = "";

        // iterate through address components to find area, city, and postal code
        results[0].address_components.forEach((component) => {
          if (
            component.types.includes("premise") ||
            component.types.includes("street_number") ||
            component.types.includes("route")
          ) {
            street = `${street}${street && component.long_name ? " " : ""}${
              component.long_name
            }`;
          }
          if (
            component.types.includes("sublocality") ||
            component.types.includes("neighborhood")
          ) {
            area = component.long_name;
          }
          if (component.types.includes("locality")) {
            city = component.long_name;
          }
          if (component.types.includes("country")) {
            country = component.long_name;
            countryCode = component.short_name;
          }
          if (component.types.includes("postal_code")) {
            postalCode = component.long_name;
          }
        });

        setSearchInput(concatStrings([street, area, city]));

        dispatch(
          bulkUpdateCheckoutStore({
            type: "",
            value: {
              userArea: area,
              userCity: city,
              userCountry: country,
              userCountryCode: countryCode,
              userPostalCode: postalCode,

              streetAddress: street,

              validationError: {
                ...validationError,

                streetAddress: street ? "" : validationError.streetAddress,
                userArea: area ? "" : validationError.userArea,
                userCity: city ? "" : validationError.userCity,
              },
            },
          })
        );
      } else {
        dispatch(updateCheckoutStore({ type: "apiCallError", value: true }));
      }
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const currentPosition = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };
          mapRef.current?.panTo(currentPosition);

          dispatch(
            updateCheckoutStore({
              type: "userLocation",
              value: currentPosition,
            })
          );

          reverseGeoCode(currentPosition);
        },
        (error) => {
          setError(`${error.message}`);
          setTooltipOpen(true);

          // close tooltip after 2 seconds
          setTimeout(handleTooltipClose, 2000);
        }
      );
    } else {
      alert("Geolocation is not supported by this browser.");
    }
  };

  // ==============================|| UI ||============================== //

  return isLoaded ? (
    <Box sx={{ position: "relative", mt: "10px" }}>
      <Box
        sx={{
          position: "absolute",
          top: "0%",
          left: "0%",
          zIndex: "9999",
          p: "15px 15px",
          width: "100%",
        }}
      >
        <TextField
          id="searchAddress"
          name="searchAddress"
          placeholder="Enter delivery address"
          textTransform={"capitalize"}
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
        />
      </Box>

      <Box sx={mapContainerBox}>
        <GoogleMap
          mapContainerStyle={mapContainer}
          center={userLocation}
          zoom={mapZoomLevel}
          onLoad={onLoad}
          onDragEnd={onDragEnd}
          options={googleMapOptions}
        />

        <Place sx={locationIcon} />

        <Box
          sx={{
            position: "absolute",
            bottom: "118px",
            right: "10px",
            zIndex: "99999",
            background: "#fff",
            borderRadius: "2px",
            boxShadow: "rgba(0, 0, 0, 0.3) 0px 1px 4px -1px",
            padding: "8px 7.5px 4px",
            cursor: "pointer",
          }}
        >
          <Tooltip
            open={tooltipOpen}
            onClose={handleTooltipClose}
            title={error}
          >
            <MyLocation
              sx={{ fontSize: "25px !important", color: "#000 !important" }}
              onClick={getCurrentLocation}
            />
          </Tooltip>
        </Box>
      </Box>
    </Box>
  ) : (
    <Skeleton type="map" />
  );
};

export default React.memo(GoogleMaps);
