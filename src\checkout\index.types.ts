import { LocationCoordinates, AccountDetails } from "types";

export interface PaymentMethodType {
  label: string;
  value: string;
  type: string;
  accountId: string;
  accountDetails: AccountDetails;
}

export interface PaymentDetailsType {
  stripe: StripeDetails;
  alfalahSessionId: string;
  payMobToken: string;
  payFast: PayFastDetails;
}

export interface StripeDetails {
  stripeCustomerId: string;
  selectedCardId: string;
  saveCard: boolean;
}

export interface PayFastDetails {
  merchantId: string;
  merchantName: string;
  storeId: string;
  invoiceId: string;
  token: string;
}

export interface TipTypes {
  tipPercentage: string;
  tipValue: string;
}

export interface SocialLoginsType {
  google: boolean;
  facebook: boolean;
}

export interface CheckoutFirebaseSettings {
  expireLogs: number;
}

export interface DeliveryModalProps {
  toggleDeliveryModal: boolean;
  handleToggleDeliveryModal: () => void;
}

export interface SubscriptionModalProps {
  selectedScheduleKey: string;
  toggleSubscriptionSchedule: boolean;
  handleToggleSubscriptionSchedule: () => void;
}

export interface OrderScheduleModalProps {
  toggleUpdateSchedule: boolean;
  handleToggleUpdateSchedule: () => void;
}

export interface SubscriptionDetails {
  [key: string]: { day: string; selected: boolean };
}

export interface SubscriptionType {
  type: string;
  time: string;
  details: SubscriptionDetails;
}

export interface CheckoutProcessType {
  id: number;
  successFlag: boolean;
  errorFlag: boolean;
  errorCode: number;
  statusMessage: string;
  message: string;
  reloadPaymentMethod: boolean;
}

export interface StripeCustomerInfo {
  customer_id: string;
  card_id: string;
}

export interface RequestPayload<T> {
  data: T;
}

export interface RequestParams<T> {
  params: T;
}

interface Options {
  name: string;
  price: string;
  quantity: string;
  inner_options: Options[];
}

type OptionSet = { [key: string]: Options[] } | {};

export interface CartItem {
  menu_item_id: number;
  product_name: string;
  price: number;
  qty: number;
  item_category_id: number;
  category_name: string;
  item_brand_id: number;
  tax: string;
  tax_percentage: string;
  item_level_tax_value: number;
  discount: number;
  discount_percentage: string;
  item_level_discount_value: number;
  product_code: string;
  slug: string;
  weight_value: number;
  weight_unit: string;
  note: string;
  options: OptionSet;
}

export interface CartCalculationPayload {
  flag: boolean;

  unique_order_id: string;
  business_id: string;
  branch_id: string;
  uid: string;

  name: string;
  email: string;
  phone: string;
  country: string;
  city: string;
  area: string;
  usr_lat: string;
  usr_lng: string;
  postal_code: string;
  order_type: string;
  payment_type: number;
  service_charges: string;
  tax: number;
  tax_value: number;
  discount: number;
  discount_value: number;
  voucher_type: number;
  coupon_code: string;
  tip: number;
  sub_total: number;
  grand_total: number;
  items: CartItem[];
}

export interface OrderItem {
  odetailid: number;
  orderid: number;
  id: number;
  name: string;
  qty: number;
  price: number;
  total: number;
  item_level_grand_total: number;
  comment: string;
  category_id: number;
  category_name: string;
  brand_id: number;
  product_code: string;
  option_set: OptionSet;
  discount: string;
  item_level_discount_value: number;
  coupon_discount: number;
  coupon_discount_value: number;
  tax: string;
  item_level_tax_value: number;
  adjustment_amount: number;
  weight_value: string;
  weight_unit: string;
}

export interface CreateOrderPayload {
  temp_order_id: string;
  orderid: string;
  td_order_id: string;
  business_id: string;
  bid: string;
  uid: string;
  name: string;
  email: string;
  mobile_phone: string;
  cnic: string;
  city: string;
  area: string;
  address: string;
  user_latitude: string;
  user_longitude: string;
  postal_code: string;
  note: string;
  payment_type: number;
  pre_auth: number;
  source: string;
  ordertype: string;
  order_type: string;
  order_type_flag: string;
  delivery: string;
  delivery_date: string;
  discount: number;
  discount_value: number;
  tax: number;
  tax_value: number;
  delivery_charges: number;
  delivery_tax: number;
  delivery_tax_value: number;
  tip: number;
  service_charges: string;
  canada_post: string;
  loyalty_points: string;
  custom_code: string;
  custom_code_id: string;
  custom_code_type: string;
  custom_code_discount: number;
  custom_code_discount_value: number;
  total: number;
  gtotal: number;
  items: OrderItem[];
}

export interface PaymentChargePayload {
  order_id: number;
  business_id: string;
  branch_id: string;
  email: string;
  amount: number;
  currency_code: string;
  payment_type: string;
  token: string;
  return_url: string;
  user_id: string;
  customer_id: string;
  pre_auth: string;
  source: string;
}

interface SubscriptionPaymentDetails {
  userId: string;
  customerId: string;
  cardId: string;
  refreshToken: string;
}

export type SubscriptionSchedule = string | { [key: string]: string };

export interface SubscriptionPayload {
  scheduleType: string;
  schedule: SubscriptionSchedule;
  timeZone: string;
  businessId: string;
  paymentDetails: SubscriptionPaymentDetails;
  orderDetails: CreateOrderPayload;
}

export interface SignUpPayload {
  name: string;
  email: string;
  phone: string;
  password: string;
  address: string;
  city: string;
  gender: string;
  dob: string;
  token: string;
  source: string;
  device: string;
  facebook_id: string;
  google_id: string;
  apple_id: string;
}

export interface LoginPayload {
  email: string;
  password: string;
  source: string;
}

export interface RegisterStripeCustomerPayload {
  order_id: number;
  business_id: string;
  branch_id: string;
  email: string;
  amount: number;
  payment_type: string;
  token: string;
  name: string;
  return_url: string;
  user_id: string;
  customer_id: string;
  save_card: boolean;
  pre_auth: string;
  source: string;
}

export interface UserAuthDetails {
  name: string;
  email: string;
  phone: string;
  userId: string;
  tdUserId: string;
  authToken: string;
  renewAuthToken: string;
  paymentDetails: PaymentDetailsType;
}

export interface UserAuthResponse {
  user_id: string;
  user_email: string;
  user_fullname: string;
  user_gender: string;
  user_address: string;
  user_fbid: string;
  app_secret: string;
  user_dob: string;
  user_cphone: string;
  td_user_id: string;
  user_city: string;
  payment_settings?: { stripe?: string };
  token: string;
  refresh_token: string;
}

export interface CalculatedCartItem {
  menu_item_id: number;
  product_name: string;
  price: number;
  qty: number;
  total: number;

  item_category_id: number;
  category_name: string;
  item_brand_id: number;
  product_code: string;
  weight_value: number;
  weight_unit: string;
  calculated_weight: number;
  note: string;

  tax: number;
  tax_percentage: string;
  item_level_tax_value: number;

  discount: number;
  discount_percentage: number;
  item_level_discount_value: number;

  coupon_discount: number;
  coupon_percentage: number;
  slug: string;
  options: OptionSet;
}

export interface CalculatedCartResponse {
  sourceFlag: boolean;

  name: string;
  email: string;
  phone: string;
  country: string;
  city: string;
  area: string;
  usr_lat: string;
  usr_lng: string;
  postal_code: string;
  business_id: number;
  branch_id: string;
  order_type: string;
  payment_type: number;
  service_charges: string;
  tax: number;
  tax_value: number;
  discount: number;
  discount_value: number;
  coupon_code: string;
  tip: number;
  items: CalculatedCartItem[];
  coupon_discount: number;
  coupon_discount_value: number;
  distance: number;
  delivery_charges: number;
  total: number;
  grand_total: number;
}

export interface UserSavedData {
  phone: string;
  name: string;
  email: string;
  userId: string;
  tdUserId: string;
  authToken: string;
  renewAuthToken: string;
  paymentDetails: PaymentDetailsType;
  address: string;
  apartment: string;
  userArea: string;
  userCity: string;
  userPostalCode: string;
  userState: string;
  userCountry: string;
  userLocation: LocationCoordinates;
}

export interface PaymentOrderDetails {
  orderType: string;
  paymentType: PaymentMethodType;
}

export interface SelectedSubscriptionInfo {
  subscriptionDetails: SubscriptionDetails;
  subscriptionKey: string;
}

export interface OrderValidation {
  name: boolean;
  email: boolean;
  phone: boolean;
  deliveryAddress: boolean;
  orderSchedule: number | boolean;
  streetAddress: boolean;
  userArea: boolean;
  userCity: boolean;
  userPostalCode: boolean;
  coupon: string;
  subscription: boolean;

  [key: string]: string | number | boolean;
}

export interface ElementIds {
  name: string;
  email: string;
  phone: string;
  deliveryAddress: string;
  orderSchedule: string;
  subscription: string;
  coupon: string;
  bankTransfer: string;

  [key: string]: string;
}

export interface SubmitFormProps {
  styles: object;
}
