import { initializeApp } from "firebase/app";
import {
  getAuth,
  GoogleAuthProvider,
  signInWithPopup,
  signInWithRedirect,
  getRedirectResult,
  FacebookAuthProvider,
} from "firebase/auth";
import { Firestore, getFirestore } from "firebase/firestore";
import { getRemoteConfig, RemoteConfig } from "firebase/remote-config";

// initialize Firebase
const app = initializeApp(
  JSON.parse(process.env.REACT_APP_FIREBASE_CONFIG || "{}")
);

// get auth instance
const auth = getAuth(app);

// get remote config instance
const remoteConfig: RemoteConfig = getRemoteConfig(app);

// get fireStore database instance
const fireStoreDB: Firestore = getFirestore(app);

// create provider instances
const googleProvider = new GoogleAuthProvider();
const facebookProvider = new FacebookAuthProvider();

// set custom parameter to always show the account picker (even for 1 account)
googleProvider.setCustomParameters({ prompt: "select_account" });

export {
  auth,
  googleProvider,
  signInWithPopup,
  signInWithRedirect,
  getRedirectResult,
  facebookProvider,
  remoteConfig,
  fireStoreDB,
};
