import moment from "moment";

import {
  CartCalculationPayload,
  CreateOrderPayload,
  LoginPayload,
  PaymentChargePayload,
  PaymentDetailsType,
  RegisterStripeCustomerPayload,
  RequestPayload,
  SignUpPayload,
  SubscriptionPayload,
  UserAuthDetails,
  UserAuthResponse,
  CalculatedCartResponse,
  CalculatedCartItem,
} from "checkout/index.types";
import { CartDetailsType, RequestConfig } from "types";
import {
  ForgotPasswordDetails,
  LoginDetails,
  SignUpDetails,
} from "checkout/manageAccount/index.types";
import {
  capitalizeFirstLetter,
  concatStrings,
  convertTimeZoneOffset,
  getPreAuthValue,
  getSubscriptionScheduleJSON,
  updateCookies,
} from "./helperFunctions";
import {
  dateTimeFormats,
  orderTypes,
  sourceTypes,
  subscriptionTypes,
} from "./constant";

import { dispatch } from "store";
import {
  bulkUpdateCheckoutStore,
  CheckoutTypes,
  updateCheckoutStore,
} from "store/slices/checkout";
import { BusinessTypes } from "store/slices/business";

// ==============================|| API endPoints ||============================== //

// get order cart details
export const getCart = (businessId: string, cartId: string): RequestConfig => ({
  url: `${process.env.REACT_APP_CART}/business/${businessId}/cart/${cartId}`,
  method: "GET",
});

// get cart calculations
export const getCartCalculations = (): RequestConfig => ({
  url: `${process.env.REACT_APP_CART}/cart/calculate`,
  method: "POST",
});

// get business details
export const getBusinessData = (
  businessId: string,
  branchId: string
): RequestConfig => ({
  url: `${process.env.REACT_APP_BUSINESS}/${businessId}/locations/${branchId}`,
  method: "GET",
});

// get branch pickup time slots
export const getPickupSlots = (): RequestConfig => ({
  url: "/pickup_hours_time_slots",
  method: "GET",
});

// payments base url
export const onlinePayment = (
  businessId: string,
  paymentType: string
): RequestConfig => ({
  url: `${process.env.REACT_APP_BUSINESS}/${businessId}/payment/${paymentType}/charge`,
  method: "POST",
});

// create order
export const createOrder = (businessId: string): RequestConfig => ({
  url: `${process.env.REACT_APP_BUSINESS}/${businessId}/order`,
  method: "POST",
});

// destroy php session (for obw)
export const destroyOBWSession = (siteUrl: string): RequestConfig => ({
  url: `${siteUrl}/destroy-cart`,
  method: "GET",
});

// subscribe order schedule
export const subscribeOrderSchedule = (): RequestConfig => ({
  url: `${process.env.REACT_APP_API_GATEWAY}/scheduler/order/schedule-order`,
  method: "POST",
});

// sign up
export const signUp = (businessId: string): RequestConfig => ({
  url: `${process.env.REACT_APP_CART}/business/${businessId}/user/signup`,
  method: "POST",
});

// login
export const login = (businessId: string): RequestConfig => ({
  url: `${process.env.REACT_APP_CART}/business/${businessId}/user/login`,
  method: "POST",
});

// forgot password
export const forgotPassword = (businessId: string): RequestConfig => ({
  url: `${process.env.REACT_APP_CART}/business/${businessId}/user/forgot-password`,
  method: "POST",
});

// renew customer auth token
export const renewCustomerAuthToken = (
  businessId: string,
  userId: string
): RequestConfig => ({
  url: `${process.env.REACT_APP_CART}/business/${businessId}/user/${userId}/token`,
  method: "POST",
});

// register customer (card) on stripe
export const stripeCustomerRegistration = (
  businessId: string,
  paymentType: string
): RequestConfig => ({
  url: `${process.env.REACT_APP_BUSINESS}/${businessId}/payment/${paymentType}/register_stripe_customer`,
  method: "POST",
});

// get customer registered cards from stripe
export const getStripeCardsList = (
  businessId: string,
  paymentType: string,
  customerId: string
): RequestConfig => ({
  url: `${process.env.REACT_APP_BUSINESS}/${businessId}/payment/${paymentType}/customer/${customerId}/cards`,
  method: "GET",
});

// get user's social login data
export const getSocialLogin = (
  businessId: string,
  name: string,
  email: string,
  socialId: string,
  socialPlatform: string
): RequestConfig => ({
  url: `${process.env.REACT_APP_CART}/business/${businessId}/user/${email}/${socialPlatform}/${socialId}/login`,
  method: "GET",
});

// get social provider URL
export const getSocialProvider = (socialPlatform: string): RequestConfig => ({
  url: `${process.env.REACT_APP_SOCIAL_PROVIDER}/${socialPlatform}?domain=${window.location.href}`,
  method: "GET",
});

// ==============================|| API payloads ||============================== //

// cart calculation payload
export const getCartCalculationPayload = (
  checkoutDetails: CheckoutTypes,
  paymentType: string,
  orderType: string,
  couponCode: string,
  tip: number
): RequestPayload<CartCalculationPayload> => {
  return {
    data: {
      // using this flag to differentiate item level discount calculation b/w 'OBW' and 'ORDRZ'
      flag:
        checkoutDetails?.source === sourceTypes?.WEB_OBW &&
        !checkoutDetails?.cartDetails?.sourceFlag
          ? true
          : false,

      unique_order_id: checkoutDetails?.cartDetails?.temp_order_id,
      business_id: checkoutDetails?.businessId,
      branch_id: checkoutDetails?.branchId,
      uid: checkoutDetails?.tdUserId,

      name: checkoutDetails?.name,
      email: checkoutDetails?.email,
      phone: checkoutDetails?.phone,

      country:
        orderType === orderTypes?.DELIVERY ? checkoutDetails?.userCountry : "",
      city: orderType === orderTypes?.DELIVERY ? checkoutDetails?.userCity : "",
      area: orderType === orderTypes?.DELIVERY ? checkoutDetails?.userArea : "",

      usr_lat: checkoutDetails?.userLocation.lat.toString(),
      usr_lng: checkoutDetails?.userLocation.lng.toString(),
      postal_code: "",

      order_type: orderType,
      /**
       * payment type 0 => in case of cod
       * payment type 1 => in case of online payments
       * payment_type => key used to toggle online payment tax
       */
      payment_type: Number(paymentType) ? 1 : 0,
      service_charges: "0",

      tax: checkoutDetails?.cartDetails.tax,
      tax_value: checkoutDetails?.cartDetails.tax_value,
      discount: checkoutDetails?.cartDetails.discount,
      discount_value: checkoutDetails?.cartDetails.discount_value,

      /**
       * voucher_type 0 => in case of no subscription
       * voucher_type 1 => in case of subscription
       */
      voucher_type: Object.values(subscriptionTypes)?.includes(
        checkoutDetails?.subscription?.type
      )
        ? 1
        : 0,
      coupon_code: couponCode,
      tip: tip || 0,

      sub_total: checkoutDetails?.cartDetails.total,
      grand_total: checkoutDetails?.cartDetails.gtotal,

      items: checkoutDetails?.cartDetails.items?.map((item) => ({
        menu_item_id: item.menu_item_id,
        product_name: item.dname,
        price: item.dprice || 0,
        qty: item.dqty || 0,

        item_category_id: item.category_id,
        category_name: item.category_name || "",
        item_brand_id: item.brand_id,

        tax: item.tax || "0",
        tax_percentage: item.tax || "0",
        item_level_tax_value: item.item_level_tax_value || 0,

        discount: item.item_level_discount_value || 0,
        discount_percentage: item.discount || "0",
        item_level_discount_value: item.item_level_discount_value || 0,

        product_code: item.product_code ? item.product_code : "",
        slug: `menu_item_id_${item.menu_item_id}`,
        weight_value: item.weight_value ? Number(item.weight_value) : 0,
        weight_unit: item.weight_unit,
        calculated_weight: item.calculated_weight || 0,
        note: item.comment ? item.comment : "",

        options:
          typeof item.option_set === "string" && JSON.parse(item.option_set)
            ? JSON.parse(item.option_set)
            : {},
      })),
    },
  };
};

// create order API payload
export const createOrderAPIPayload = (
  checkoutDetails: CheckoutTypes,
  branchDetails: BusinessTypes["branchDetails"]
): RequestPayload<CreateOrderPayload> => {
  return {
    data: {
      temp_order_id: checkoutDetails?.cartDetails?.temp_order_id,
      orderid: checkoutDetails?.cartDetails?.orderid?.toString(),
      td_order_id: "0",

      business_id: checkoutDetails?.businessId,
      bid: checkoutDetails?.branchId,
      uid: checkoutDetails?.tdUserId,

      name: capitalizeFirstLetter(checkoutDetails?.name),
      email: checkoutDetails?.email,
      mobile_phone: checkoutDetails?.phone,
      cnic: "",

      city:
        checkoutDetails?.orderType === orderTypes?.DELIVERY
          ? capitalizeFirstLetter(checkoutDetails?.userCity)
          : branchDetails?.city,
      area:
        checkoutDetails?.orderType === orderTypes?.DELIVERY
          ? capitalizeFirstLetter(checkoutDetails?.userArea)
          : branchDetails?.area,
      address:
        checkoutDetails?.orderType === orderTypes?.DELIVERY
          ? checkoutDetails?.address
          : concatStrings([
              branchDetails?.address,
              branchDetails?.area,
              branchDetails?.city,
              branchDetails?.country,
            ]),
      user_latitude: checkoutDetails?.userLocation?.lat.toString(),
      user_longitude: checkoutDetails?.userLocation?.lng.toString(),
      postal_code: checkoutDetails?.userPostalCode,
      note: checkoutDetails?.specialInstructions?.trim(),

      payment_type: Number(checkoutDetails?.paymentType.value),
      pre_auth: getPreAuthValue(checkoutDetails?.paymentType),

      source:
        (checkoutDetails?.source === sourceTypes?.APP && sourceTypes?.APP) ||
        sourceTypes?.WEB,
      ordertype: checkoutDetails?.orderType,
      order_type: checkoutDetails?.orderType,
      order_type_flag:
        checkoutDetails?.orderType === orderTypes?.DELIVERY ? "0" : "1",

      delivery:
        checkoutDetails?.orderType === orderTypes?.DELIVERY
          ? convertTimeZoneOffset(
              branchDetails?.timeZone,
              `${dateTimeFormats?.DATE} ${dateTimeFormats?.TIME}`
            )
          : `${checkoutDetails?.orderSchedule?.date} ${moment(
              checkoutDetails?.orderSchedule?.time,
              `${dateTimeFormats?.TIME_MERIDIAN}`
            )?.format(`${dateTimeFormats?.TIME}`)}`,
      delivery_date:
        checkoutDetails?.orderType === orderTypes?.DELIVERY &&
        checkoutDetails?.orderSchedule?.date
          ? `${checkoutDetails?.orderSchedule?.date} ${moment(
              checkoutDetails?.orderSchedule?.time,
              `${dateTimeFormats?.TIME_MERIDIAN}`
            )?.format(`${dateTimeFormats?.TIME}`)}`
          : `${dateTimeFormats?.INITIAL_DATE_TIME}`,

      discount: checkoutDetails?.cartDetails?.discount,
      discount_value: checkoutDetails?.cartDetails?.discount_value,
      tax: checkoutDetails?.cartDetails?.tax,
      tax_value: checkoutDetails?.cartDetails?.tax_value,
      delivery_charges: checkoutDetails?.cartDetails?.delivery_charges,
      delivery_tax: checkoutDetails?.cartDetails?.delivery_tax,
      delivery_tax_value: checkoutDetails?.cartDetails?.delivery_tax_value,
      tip: checkoutDetails?.cartDetails?.tip || 0,
      service_charges: "0",
      canada_post: "0",

      loyalty_points: "0",

      custom_code: checkoutDetails?.couponCode,
      custom_code_id: "0",
      custom_code_type: "2",
      custom_code_discount: checkoutDetails?.cartDetails?.coupon_discount || 0,
      custom_code_discount_value:
        checkoutDetails?.cartDetails?.coupon_discount_value || 0,

      total: checkoutDetails?.cartDetails?.total,
      gtotal: checkoutDetails?.cartDetails?.gtotal,

      items: checkoutDetails?.cartDetails?.items?.map((item) => ({
        odetailid: item.odetailid || 0,
        orderid: item.orderid || 0,

        id: item.menu_item_id,
        name: item.dname,
        qty: item.dqty,
        price: item.dprice,
        total: item.dtotal,
        item_level_grand_total: item.item_level_grand_total,
        comment: item.comment ? item.comment : "",

        category_id: item.category_id,
        category_name: item.category_name || "",
        brand_id: item.brand_id,
        product_code: item.product_code || "",

        option_set:
          typeof item.option_set === "string" && JSON.parse(item.option_set)
            ? JSON.parse(item.option_set)
            : {},

        discount: item.discount,
        item_level_discount_value: item.item_level_discount_value,
        coupon_discount: item.coupon_discount || 0,
        coupon_discount_value: item.coupon_discount_value || 0,
        tax: item.tax,
        item_level_tax_value: item.item_level_tax_value,
        adjustment_amount: item.adjustment_amount,
        weight_value: item.weight_value ? item.weight_value.toString() : "0",
        weight_unit: item.weight_unit,
      })),
    },
  };
};

// subscription API payload
export const subscriptionAPIPayload = (
  checkoutDetails: CheckoutTypes,
  branchDetails: BusinessTypes["branchDetails"],
  customerId: string,
  cardId: string
): RequestPayload<SubscriptionPayload> => {
  return {
    data: {
      scheduleType: checkoutDetails?.subscription?.type,
      schedule: getSubscriptionScheduleJSON(checkoutDetails?.subscription),
      timeZone: branchDetails?.timeZone,
      businessId: checkoutDetails?.businessId?.toString(),
      paymentDetails: {
        userId: checkoutDetails?.userId,
        customerId,
        cardId,
        refreshToken: checkoutDetails?.renewAuthToken,
      },

      orderDetails: createOrderAPIPayload(checkoutDetails, branchDetails)?.data,
    },
  };
};

// payment charge API payload
export const paymentChargeAPIPayload = (
  orderId: number,
  businessId: string,
  branchId: string,
  email: string,
  total: number,
  currencyCode: string,
  type: string,
  token: string,
  returnURL: string,
  userId: string,
  customerId: string
): RequestPayload<PaymentChargePayload> => {
  return {
    data: {
      order_id: orderId,
      business_id: businessId,
      branch_id: branchId,
      email: email,
      amount: total,
      currency_code: currencyCode,
      payment_type: type,
      token: token,
      return_url: returnURL,
      user_id: userId,
      customer_id: customerId,
      pre_auth: "false",
      source: sourceTypes?.WEB,
    },
  };
};

// register stripe customer API payload
export const registerStripeCustomerAPIPayload = (
  orderId: number,
  businessId: string,
  branchId: string,
  email: string,
  total: number,
  paymentType: string,
  token: string,
  name: string,
  returnURL: string,
  userId: string,
  customerId: string,
  saveCard: boolean
): RegisterStripeCustomerPayload => {
  return {
    order_id: orderId,
    business_id: businessId,
    branch_id: branchId,
    email: email,
    amount: total,
    payment_type: paymentType,
    token: token,
    name: capitalizeFirstLetter(name),
    return_url: returnURL,
    user_id: userId,
    customer_id: customerId,
    save_card: saveCard,
    pre_auth: "false",
    source: sourceTypes?.WEB,
  };
};

// sign up API payload
export const signUpAPIPayload = (
  userDetails: SignUpDetails
): RequestPayload<SignUpPayload> => {
  return {
    data: {
      name: capitalizeFirstLetter(userDetails?.name || ""),
      email: userDetails?.email,
      phone: userDetails?.phone,
      password: userDetails?.password,
      address: "",
      city: "",
      gender: "",
      dob: "",
      token: "",
      source: sourceTypes?.WEB,
      device: sourceTypes?.WEB,
      facebook_id: userDetails?.facebookId || "",
      google_id: userDetails?.googleId || "",
      apple_id: "",
    },
  };
};

// login API payload
export const loginAPIPayload = (
  userDetails: LoginDetails
): RequestPayload<LoginPayload> => {
  return {
    data: {
      email: userDetails?.email,
      password: userDetails?.password,
      source: sourceTypes?.WEB,
    },
  };
};

// login API payload
export const forgotPasswordAPIPayload = (
  userDetails: ForgotPasswordDetails
): RequestPayload<ForgotPasswordDetails> => {
  return {
    data: {
      email: userDetails?.email,
      domain: window?.location?.hostname || "",
    },
  };
};

// ==============================|| API responses ||============================== //

// user auth response
export const setUserAuthResponse = (
  paymentDetails: PaymentDetailsType,
  userDetails: UserAuthResponse
): void => {
  const userAuthResponse: UserAuthDetails = {
    name: userDetails?.user_fullname,
    email: userDetails?.user_email,
    phone: userDetails?.user_cphone,

    userId: userDetails?.user_id,
    tdUserId: userDetails?.td_user_id,
    authToken: userDetails?.token,
    renewAuthToken: userDetails?.refresh_token,

    // stripeCustomerId => if customer is registered on stripe (card saved on stripe)
    paymentDetails: {
      ...paymentDetails,
      stripe: {
        ...paymentDetails?.stripe,
        stripeCustomerId: userDetails?.payment_settings?.stripe || "",
      },
    },
  };

  // set user details in store
  dispatch(bulkUpdateCheckoutStore({ type: "", value: userAuthResponse }));

  // set data in cookies
  updateCookies(userAuthResponse);
};

// cart calculation response
export const setCartCalculationResponse = (
  cartDetails: CartDetailsType,
  calculatedCartDetails: CalculatedCartResponse
): void => {
  const cartCalculationResponse: CartDetailsType = {
    ...cartDetails,

    // using this flag to differentiate item level discount calculation b/w 'OBW' and 'ORDRZ'
    sourceFlag: businessInfo?.source === sourceTypes?.WEB_OBW,

    total: calculatedCartDetails?.total,
    gtotal: calculatedCartDetails?.grand_total,

    tax: calculatedCartDetails?.tax || 0,
    tax_value: calculatedCartDetails?.tax_value || 0,

    discount: calculatedCartDetails?.discount || 0,
    discount_value: calculatedCartDetails?.discount_value || 0,

    coupon_discount: calculatedCartDetails?.coupon_discount || 0,
    coupon_discount_value: calculatedCartDetails?.coupon_discount_value || 0,

    tip: calculatedCartDetails?.tip,
    delivery_charges: calculatedCartDetails?.delivery_charges,

    items: calculatedCartDetails?.items?.map(
      (item: CalculatedCartItem, index: number) => ({
        ...cartDetails?.items[index],

        menu_item_id: item.menu_item_id,
        dname: item.product_name,
        dprice: item.price,
        dqty: item.qty,
        dtotal: item.total,

        category_id: item.item_category_id,
        brand_id: item.item_brand_id,

        tax: item.tax_percentage?.toString() || "0",
        item_level_tax_value: item.item_level_tax_value || 0,

        discount: item.discount_percentage?.toString() || "0",
        item_level_discount_value: item.item_level_discount_value || 0,

        coupon_discount: item.coupon_percentage,
        coupon_discount_value: item.coupon_discount,

        product_code: item.product_code,
        weight_value: item.weight_value,
        weight_unit: item.weight_unit,
        calculated_weight: item.calculated_weight || 0,
        comment: item.note,

        option_set: JSON.stringify(item.options),
      })
    ),
  };

  // set calculated cart to store
  dispatch(
    updateCheckoutStore({ type: "cartDetails", value: cartCalculationResponse })
  );
};
