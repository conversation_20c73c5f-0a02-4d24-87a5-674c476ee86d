import { useEffect, useRef } from "react";

import {
  CircularProgress,
  Typography,
  Stack,
  FormControlLabel,
  Radio,
  RadioGroup,
  Paper,
  FormControl,
} from "@mui/material";

import { useCartCalculation } from "customHooks/useCartCalculation";
import {
  paperStyles,
  paymentHeadingStyles,
  paymentLoaderStyles,
  paymentLabelStack,
  paymentRadioParentStyles,
} from "../index.styles";
import { ErrorResponse } from "types";
import { PaymentMethodType } from "checkout/index.types";
import {
  deliverySettingOptions,
  orderTypes,
  paymentMethods,
  UIElementIds,
} from "utils/constant";
import {
  getPaymentLabel,
  getRadioStyles,
  scrollToBottom,
  scrollToElement,
  userNotification,
} from "utils/helperFunctions";
import { getCartCalculationPayload } from "utils/apiConfig";

import Stripe from "integrations/payments/stripe";
import BankTransfer from "integrations/payments/bankTransfer";
import { dispatch, useSelector } from "store";
import {
  bulkUpdateCheckoutStore,
  updateCheckoutStore,
} from "store/slices/checkout";

const PaymentDetails = () => {
  const checkoutDetails = useSelector((state) => state.checkout);

  const { businessDetails, branchDetails } = useSelector(
    (state) => state.business
  );

  const prevPaymentMethod = useRef<PaymentMethodType>(
    checkoutDetails?.paymentType
  );

  // ==============================|| custom hooks ||============================== //

  // custom hook to call cart calculation API
  const { getCartCalculationsLoading, getCartCalculationsAPICall } =
    useCartCalculation();

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function call to scroll window to bottom
    scrollToBottom(
      checkoutDetails?.paymentType,
      businessDetails?.paymentMethods,
      getCartCalculationsLoading
    );
  }, [
    checkoutDetails?.paymentType,
    businessDetails?.paymentMethods,
    getCartCalculationsLoading,
  ]);

  // ==============================|| handler functions ||============================== //

  // function to handle the cart calculation API
  const callCartCalculationAPI = async (
    paymentType: PaymentMethodType
  ): Promise<void> => {
    // API call to calculate cart
    const result: boolean | ErrorResponse = await getCartCalculationsAPICall(
      getCartCalculationPayload(
        checkoutDetails,
        paymentType.value,
        checkoutDetails.orderType,
        checkoutDetails.couponCode,
        checkoutDetails.cartDetails.tip
      ),
      checkoutDetails?.cartDetails
    );

    /**
     * set previously selected payment method on failure of calculation API
     * doing this to sync with the online payment tax if any
     */
    if (typeof result !== "boolean") {
      // function to set message to notify user
      userNotification("Failed to update payment method!");

      /**
       * set previously selected payment method
       * set validation error to 'deliveryAddress' in case of delivery (calculation API fails because of missing delivery address)
       */
      dispatch(
        bulkUpdateCheckoutStore({
          type: "",
          value: {
            paymentType: prevPaymentMethod?.current,
            validationError: {
              ...checkoutDetails?.validationError,

              deliveryAddress:
                checkoutDetails?.orderType === orderTypes?.DELIVERY
                  ? true
                  : false,
            },
          },
        })
      );

      // function to scroll window to specified element
      scrollToElement(UIElementIds?.deliveryAddress);
    }
  };

  // function to handle the payment method change
  const handlePaymentTypeChange = (event: {
    target: { name: string; value: string };
  }): void => {
    // find the selected payment type object
    const selectedPaymentType: PaymentMethodType | undefined =
      businessDetails.paymentMethods?.find(
        (paymentType) => paymentType.type === event.target.name
      );

    // storing the previous payment type to reset in case of calculation API failure
    prevPaymentMethod.current = checkoutDetails?.paymentType;

    // set data in store
    dispatch(
      updateCheckoutStore({
        type: "paymentType",
        value: selectedPaymentType || prevPaymentMethod.current,
      })
    );

    /**
     * call cart calculation API to calculate online payment tax if any
     * restrict API call for delivery if coordinates not found to avoid API exception
     */
    if (
      (checkoutDetails.userLocation.lat &&
        checkoutDetails.orderType === orderTypes?.DELIVERY &&
        branchDetails?.deliverySettings?.charges_type ===
          deliverySettingOptions?.GEO_RANGE) ||
      branchDetails?.deliverySettings?.charges_type !==
        deliverySettingOptions?.GEO_RANGE ||
      checkoutDetails.orderType === orderTypes?.PICK_UP
    ) {
      callCartCalculationAPI(selectedPaymentType || prevPaymentMethod.current);
    }
  };

  // ==============================|| UI ||============================== //

  // function to return the payment details of respective payment method
  const getPaymentDetails = (): JSX.Element => {
    // switch to respective payment method
    switch (checkoutDetails?.paymentType.type?.toLowerCase()) {
      case paymentMethods?.STRIPE?.value: {
        return <Stripe />;
      }
      case paymentMethods?.BANK_TRANSFER?.value: {
        return <BankTransfer />;
      }
      default: {
        return <></>;
      }
    }
  };

  // function to display the payment label with loader on API call
  const displayPaymentLabel = (
    paymentOption: PaymentMethodType
  ): JSX.Element => {
    return (
      <Stack sx={paymentLabelStack}>
        {/* get the payment label */}
        {getPaymentLabel(paymentOption, checkoutDetails?.orderType)}

        {/* set the loader on selected payment label on API loading state true */}
        {paymentOption?.label === checkoutDetails?.paymentType?.label &&
          getCartCalculationsLoading && (
            <CircularProgress size={16} sx={paymentLoaderStyles} />
          )}
      </Stack>
    );
  };

  return (
    <Paper elevation={0} sx={paperStyles}>
      <Typography component="strong" sx={paymentHeadingStyles}>
        Payment details
      </Typography>

      <Stack spacing={2.5}>
        <FormControl component="fieldset">
          <RadioGroup
            value={checkoutDetails?.paymentType.type}
            onChange={handlePaymentTypeChange}
            sx={paymentRadioParentStyles}
          >
            {businessDetails?.paymentMethods?.map(
              (paymentOption: PaymentMethodType) => (
                <FormControlLabel
                  key={paymentOption.label}
                  name={paymentOption.type}
                  value={paymentOption.type}
                  control={<Radio />}
                  label={displayPaymentLabel(paymentOption)}
                  disabled={checkoutDetails?.disableComponentFlag}
                  sx={getRadioStyles(
                    checkoutDetails?.paymentType?.label.includes(
                      paymentOption.label
                    )
                  )}
                />
              )
            )}
          </RadioGroup>
        </FormControl>

        {/* get payment details of respective payment method */}
        {getPaymentDetails()}
      </Stack>
    </Paper>
  );
};

export default PaymentDetails;
