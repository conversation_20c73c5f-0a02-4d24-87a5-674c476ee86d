import { TextField, Autocomplete as MUIAutocomplete, Box } from "@mui/material";

import {
  autocompleteErrorStyles,
  autocompleteParentStyle,
  autocompleteStyles,
} from "components/index.styles";

const Autocomplete = ({
  label,
  value,
  options,
  onChange,
  sx,
  boxSX,
  helperText,
  error,
}: {
  error?: boolean;
  label?: string;
  value: string;
  helperText?: string;
  options: string[];
  disabled?: boolean;
  isMultiSelect?: boolean;
  onChange?: (event: React.ChangeEvent<{}>, value: any, name: string) => void;
  popupIcon?: React.ReactNode;
  loading?: boolean;
  boxSX?: object;
  sx?: object;
}) => {
  return (
    <Box sx={{ ...autocompleteParentStyle, ...boxSX }}>
      <MUIAutocomplete
        value={value}
        onChange={onChange}
        options={options}
        clearIcon={null}
        renderInput={(params) => (
          <TextField {...params} label={label} variant="outlined" />
        )}
        sx={{ ...autocompleteStyles, ...sx }}
      />

      {error && (
        <Box sx={autocompleteErrorStyles}>{helperText || "Required"}</Box>
      )}
    </Box>
  );
};

export default Autocomplete;
