import { useState } from "react";

import { Box, Stack, Typography } from "@mui/material";

import { useCartCalculation } from "customHooks/useCartCalculation";

import Button from "components/button";
import TextField from "components/textField";
import ManualAuth from "checkout/manageAccount/manualAuth";

import { errorMessageStyles } from "styles";
import {
  couponFieldBox,
  couponTipField,
  editActionStyle,
  summaryButtonStyles,
} from "../index.styles";
import {
  productsAccordionTitle,
  summarySubTotal,
} from "../cartSummary/index.styles";
import { ErrorResponse } from "types";
import { UIElementIds, userAuthTypes } from "utils/constant";
import { getCartCalculationPayload } from "utils/apiConfig";
import { dispatch, useSelector } from "store";
import {
  bulkUpdateCheckoutStore,
  updateCheckoutStore,
} from "store/slices/checkout";

const Coupon = () => {
  // using local state to prevent invalid coupon in cart calculation API payload
  const [coupon, setCoupon] = useState<string>("");
  const [isCouponApplied, setIsCouponApplied] = useState<boolean>(false);
  const [toggleUserManualAuth, setToggleUserManualAuth] =
    useState<boolean>(false);

  const checkoutDetails = useSelector((state) => state.checkout);

  // ==============================|| custom hooks ||============================== //

  // custom hook to call cart calculation API
  const { getCartCalculationsLoading, getCartCalculationsAPICall } =
    useCartCalculation();

  // ==============================|| handler functions ||============================== //

  // function to handle the cart calculation API
  const callCartCalculationAPI = async (): Promise<void> => {
    // set coupon code based on the state (apply or remove)
    const couponCode: string = isCouponApplied ? "" : coupon;

    // API call to get cart calculations
    const result: boolean | ErrorResponse = await getCartCalculationsAPICall(
      getCartCalculationPayload(
        checkoutDetails,
        checkoutDetails?.paymentType?.value,
        checkoutDetails?.orderType,
        couponCode,
        checkoutDetails?.cartDetails?.tip
      ),
      checkoutDetails?.cartDetails
    );

    // set coupon field UI and error based on response
    if (typeof result === "boolean") {
      // remove coupon code if already applied (API call for removal)
      if (isCouponApplied) {
        setCoupon("");
      }

      // toggle state to update UI
      setIsCouponApplied(!isCouponApplied);

      // set calculated cart to store & reset coupon error
      dispatch(
        bulkUpdateCheckoutStore({
          type: "cartDetails",
          value: {
            couponCode,
            validationError: {
              ...checkoutDetails?.validationError,
              coupon: "",
            },
          },
        })
      );
    } else {
      dispatch(
        updateCheckoutStore({
          type: "validationError",
          value: {
            ...checkoutDetails?.validationError,
            coupon: (result as ErrorResponse)?.message || "",
          },
        })
      );
    }
  };

  // function to handle the fields input change
  const handleInputChange = (event: {
    target: { name: string; value: string };
  }) => {
    setCoupon(event.target.value);

    // reset coupon error if any
    if (checkoutDetails?.validationError?.coupon) {
      dispatch(
        updateCheckoutStore({
          type: "validationError",
          value: { ...checkoutDetails?.validationError, coupon: "" },
        })
      );
    }
  };

  // function to submit coupon on 'Enter' key press
  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLElement> | undefined
  ): void => {
    if (event?.key === "Enter") {
      // function to call cart calculation API
      callCartCalculationAPI();
    }
  };

  // function to toggle the modal state
  const handleToggleUserManualAuth = (): void => {
    setToggleUserManualAuth((prevState) => !prevState);
  };

  // ==============================|| UI ||============================== //

  // function to return coupon message/field UI
  const getCouponFieldUI = (): JSX.Element => {
    /**
     * if => user is not logged in
     * else if => user is logged in but did not add address
     * else => show coupon field
     */
    if (!checkoutDetails?.userId) {
      return (
        <Stack direction="row" spacing={0.5}>
          <Typography sx={summarySubTotal}>To use a coupon, please</Typography>

          <Typography sx={editActionStyle} onClick={handleToggleUserManualAuth}>
            log in
          </Typography>
        </Stack>
      );
    } else if (checkoutDetails?.userLocationFlag) {
      return (
        <Typography sx={summarySubTotal}>
          To use a coupon, please update your address
        </Typography>
      );
    } else {
      return (
        <Stack direction={"row"}>
          <Box sx={couponFieldBox}>
            <TextField
              name="coupon"
              placeholder="Add coupon"
              value={coupon}
              disable={isCouponApplied}
              onChange={handleInputChange}
              styles={couponTipField}
              onKeyPress={handleEnterKeyPress}
            />
          </Box>

          <Button
            loading={getCartCalculationsLoading}
            disabled={!coupon || getCartCalculationsLoading}
            onClick={() => callCartCalculationAPI()}
            sx={summaryButtonStyles}
          >
            {isCouponApplied ? "Remove" : "Apply"}
          </Button>
        </Stack>
      );
    }
  };

  return (
    <>
      {/* login & signUp modal */}
      {toggleUserManualAuth && (
        <ManualAuth
          toggleUserManualAuth={toggleUserManualAuth}
          handleToggleUserManualAuth={handleToggleUserManualAuth}
          authTab={userAuthTypes?.LOGIN}
        />
      )}

      <Stack spacing={1.25}>
        <Stack spacing={checkoutDetails?.userLocationFlag ? 1.25 : 2.5}>
          <Typography sx={productsAccordionTitle}>Have a coupon?</Typography>

          {/* coupon field UI */}
          {getCouponFieldUI()}
        </Stack>

        {/* coupon error message */}
        {checkoutDetails?.validationError?.coupon && (
          <Typography id={UIElementIds?.coupon} sx={errorMessageStyles}>
            {checkoutDetails?.validationError?.coupon}
          </Typography>
        )}
      </Stack>
    </>
  );
};

export default Coupon;
