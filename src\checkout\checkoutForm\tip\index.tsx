import React, { useState } from "react";

import { <PERSON>ir<PERSON><PERSON>rogress, <PERSON>ack, Tab, Tabs, Typography } from "@mui/material";

import { useCartCalculation } from "customHooks/useCartCalculation";

import Button from "components/button";
import TextField from "components/textField";

import { buttonLoaderStyles } from "components/index.styles";
import { productsAccordionTitle } from "../cartSummary/index.styles";
import {
  tabsScrollStyles,
  summaryButtonStyles,
  tipsTabs,
  tipTabTypoStyle,
  couponTipField,
} from "../index.styles";
import { TipTypes } from "checkout/index.types";
import { tipsList } from "utils/constant";
import { calculatePercentageValue } from "utils/helperFunctions";
import { getCartCalculationPayload } from "utils/apiConfig";
import { useSelector } from "store";

const Tip = () => {
  const [selectedTip, setSelectedTip] = useState<TipTypes>({
    tipPercentage: "",
    tipValue: "",
  });

  const checkoutDetails = useSelector((state) => state.checkout);
  const { businessDetails } = useSelector((state) => state.business);

  // ==============================|| custom hooks ||============================== //

  // custom hook to call cart calculation API
  const { getCartCalculationsLoading, getCartCalculationsAPICall } =
    useCartCalculation();

  // ==============================|| handler functions ||============================== //

  // function to handle the cart calculation API
  const callCartCalculationAPI = async (tipValue: string) => {
    await getCartCalculationsAPICall(
      getCartCalculationPayload(
        checkoutDetails,
        checkoutDetails?.paymentType.value,
        checkoutDetails?.orderType,
        checkoutDetails?.couponCode,
        Number(tipValue)
      ),
      checkoutDetails?.cartDetails
    );
  };

  // function to handle the tip change
  const handleTipChange = (event: React.SyntheticEvent, tip: string) => {
    // function to get the value of a certain percentage
    const calculatedValue = calculatePercentageValue(
      Number(tip),
      checkoutDetails?.cartDetails?.total,
      businessDetails?.decimalPlaces
    );

    setSelectedTip({
      tipPercentage: tip,
      tipValue: tip === "Custom" ? "" : calculatedValue,
    });

    // call cart calculation API to calculate tip
    callCartCalculationAPI(calculatedValue);
  };

  // function to handle the fields input change
  const handleInputChange = (event: {
    target: { name: string; value: string };
  }) => {
    // verify input tip should be less than cart total
    if (Number(event.target.value) < checkoutDetails?.cartDetails?.total) {
      setSelectedTip({
        tipPercentage: selectedTip?.tipPercentage,
        tipValue: event.target.value,
      });
    }
  };

  // ==============================|| UI ||============================== //

  // function to return the tip figures UI
  const getTipUI = (tip: string) => {
    return (
      <>
        <Typography component="small">
          {`${tip}${tip === "Custom" ? "" : "%"}`}
        </Typography>

        {tip !== "Custom" && (
          <Typography component="small">
            {`${businessDetails?.currencyCode} ${calculatePercentageValue(
              Number(tip),
              checkoutDetails?.cartDetails.total,
              businessDetails?.decimalPlaces
            )}`}
          </Typography>
        )}
      </>
    );
  };

  // function to return the tip tabs labels
  const getTipLabel = (tip: string) => {
    return getCartCalculationsLoading &&
      selectedTip.tipPercentage === tip &&
      tip !== "Custom" ? (
      <>
        <CircularProgress
          title="Loader"
          size={24}
          sx={{
            ...buttonLoaderStyles,
            display: "block !important",
          }}
        />

        {/* get the tip figures UI */}
        {getTipUI(tip)}
      </>
    ) : (
      <>
        {/* get the tip figures UI */}
        {getTipUI(tip)}
      </>
    );
  };

  // function to return a field for custom tip input
  const getCustomTipField = () => {
    return (
      selectedTip.tipPercentage === "Custom" && (
        <Stack direction={"row"}>
          <TextField
            name="customTip"
            placeholder="Add Custom Tip"
            value={selectedTip.tipValue}
            onChange={handleInputChange}
            styles={couponTipField}
          />

          <Button
            loading={getCartCalculationsLoading}
            disabled={!selectedTip.tipValue || getCartCalculationsLoading}
            onClick={() => {
              callCartCalculationAPI(selectedTip.tipValue);
            }}
            sx={summaryButtonStyles}
          >
            Apply
          </Button>
        </Stack>
      )
    );
  };

  return (
    <>
      <Typography sx={productsAccordionTitle}>Add tip</Typography>

      <Stack spacing={2.5} sx={tabsScrollStyles}>
        <Tabs
          value={selectedTip.tipPercentage}
          onChange={handleTipChange}
          indicatorColor="secondary"
          aria-label="dynamic tabs example"
          sx={{
            ...tipsTabs,

            "& .Mui-selected": {
              color: "#FFFFFF",
              background:
                getCartCalculationsLoading &&
                selectedTip.tipPercentage !== "Custom"
                  ? "#0000001f"
                  : "#3B3B3B",
            },
          }}
          variant="scrollable"
          scrollButtons={true}
          allowScrollButtonsMobile
        >
          {tipsList?.map((tip) => (
            <Tab
              key={tip}
              value={tip}
              className="toggletabs"
              disabled={getCartCalculationsLoading}
              sx={tipTabTypoStyle}
              label={getTipLabel(tip)}
            />
          ))}
        </Tabs>

        {/* field to enter custom tip */}
        {getCustomTipField()}
      </Stack>
    </>
  );
};

export default Tip;
