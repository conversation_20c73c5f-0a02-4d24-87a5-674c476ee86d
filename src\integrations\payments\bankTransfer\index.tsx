import { Box, Stack, Typography } from "@mui/material";

import {
  productsAccordionTitle,
  summarySubTotalPrice,
} from "checkout/checkoutForm/cartSummary/index.styles";
import {
  accountInfoStack,
  bankNameStyle,
  bankParentNote,
  paymentRadioParentStyles,
} from "checkout/checkoutForm/index.styles";
import { PaymentMethodType } from "checkout/index.types";
import { BankAccountInfo, BankAccountDetails } from "types";
import { UIElementIds } from "utils/constant";
import { useSelector } from "store";

const BankTransfer = () => {
  const paymentType: PaymentMethodType = useSelector(
    (state) => state?.checkout?.paymentType
  );

  // type assertion to use in whole file
  const accountDetails: BankAccountDetails =
    paymentType?.accountDetails as BankAccountDetails;

  // store account details length to toggle UI
  const isAccountExist: number = accountDetails?.accounts?.length || 0;

  // ==============================|| UI ||============================== //

  return (
    <>
      <Stack spacing={1.25}>
        <Typography sx={productsAccordionTitle}>Account details</Typography>

        {isAccountExist > 0 && accountDetails?.note && (
          <Typography sx={bankParentNote}>{accountDetails?.note}</Typography>
        )}
      </Stack>

      {isAccountExist > 0 && (
        <Box id={UIElementIds?.bankTransfer} sx={paymentRadioParentStyles}>
          {accountDetails?.accounts?.map(
            (account: BankAccountInfo, index: number) => (
              <Stack key={index} spacing={1.25} sx={accountInfoStack}>
                <Box>
                  <Typography sx={bankNameStyle}>Bank name</Typography>
                  <Typography sx={summarySubTotalPrice}>
                    {account?.name}
                  </Typography>
                </Box>

                <Box>
                  <Typography sx={bankNameStyle}>Account title</Typography>
                  <Typography sx={summarySubTotalPrice}>
                    {account?.title}
                  </Typography>
                </Box>

                <Box>
                  <Typography sx={bankNameStyle}>Account number</Typography>
                  <Typography sx={summarySubTotalPrice}>
                    {account?.number}
                  </Typography>
                </Box>

                {account?.note && (
                  <Box>
                    <Typography sx={bankNameStyle}>Note</Typography>
                    <Typography sx={summarySubTotalPrice}>
                      {account?.note}
                    </Typography>
                  </Box>
                )}
              </Stack>
            )
          )}
        </Box>
      )}
    </>
  );
};

export default BankTransfer;
