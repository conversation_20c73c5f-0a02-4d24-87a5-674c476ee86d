import { useEffect, useRef, useState } from "react";

import { Stack, Box, Grid, Card, Typography, CardMedia } from "@mui/material";
import Item from "@mui/material/Stack";
import { AddCircle, DoNotDisturbOn } from "@mui/icons-material";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";

import { useGetStripeCards } from "customHooks/useGetStripeCards";
import { useRenewCustomerAuthToken } from "customHooks/useRenewCustomerAuthToken";

import Button from "components/button";
import StripeForm from "./stripeForm";

import {
  addCardBtnStyles,
  paymentCardsStyles,
  paymentImageStyle,
  labelStyles,
  tabContentTypoStyle,
} from "checkout/checkoutForm/index.styles";
import { getStripeCardsList } from "utils/apiConfig";
import { getCardLabel, userNotification } from "utils/helperFunctions";
import { borderColor, cardIconsPath, paymentMethods } from "utils/constant";

import { StripeCardType } from "integrations/payments/index.types";
import { dispatch, useSelector } from "store";
import { updateCheckoutStore } from "store/slices/checkout";

const Stripe: React.FC = () => {
  const [cardsList, setCardsList] = useState<StripeCardType[]>([]);
  const [toggleStripeForm, setToggleStripeForm] = useState<boolean>(false);

  let userAuthToken = useRef<string>("");

  const { businessId, paymentDetails, renewAuthToken } = useSelector(
    (state) => state.checkout
  );

  // load stripe SDK
  const stripe = loadStripe(`${process.env.REACT_APP_STRIPE_PUBLIC_KEY}`);

  // ==============================|| custom hooks ||============================== //

  // custom hook to call renew customer auth token API
  const { renewCustomerAuthTokenAPICall } = useRenewCustomerAuthToken();

  // custom hook to call get stripe cards API
  const { getStripeCardsAPICall } = useGetStripeCards();

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // check if stripe registered customer id & user auth token exists
    if (paymentDetails?.stripe?.stripeCustomerId && renewAuthToken) {
      // function to handle get registered stripe cards process
      handleGetStripeCardsProcess();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [paymentDetails?.stripe?.stripeCustomerId]);

  // ==============================|| handler functions ||============================== //

  // function to renew auth token then call get stripe cards API
  const handleGetStripeCardsProcess = async (): Promise<void> => {
    // API call to renew customer auth token
    userAuthToken.current = await renewCustomerAuthTokenAPICall();

    // verify user auth token
    if (!userAuthToken.current) {
      // function to set message to notify user
      userNotification("Failed to verify user. Please try again!");

      return;
    }

    // function to call get stripe cards list API
    callGetStripeCardsAPI();
  };

  // function to call get stripe cards list API (customer cards registered on stripe)
  const callGetStripeCardsAPI = async (): Promise<void> => {
    // API call to get customer cards registered on stripe
    const result: StripeCardType[] = await getStripeCardsAPICall({
      ...getStripeCardsList(
        businessId,
        paymentMethods?.STRIPE?.value,
        paymentDetails?.stripe?.stripeCustomerId
      ),
      headers: { Authorization: userAuthToken.current },
    });

    // update cards list state
    setCardsList(result);

    // set first card from list in store if exists
    if (result?.length) {
      dispatch(
        updateCheckoutStore({
          type: "paymentDetails",
          value: {
            ...paymentDetails,
            stripe: {
              ...paymentDetails?.stripe,
              selectedCardId: result[0]?.id,
            },
          },
        })
      );
    }
  };

  // card selection handler
  const handleCardSelectionChange = (cardId: string): void => {
    // close form on card selection if already open
    if (toggleStripeForm) {
      handleToggleStripeForm();
    }

    // set data in store
    dispatch(
      updateCheckoutStore({
        type: "paymentDetails",
        value: {
          ...paymentDetails,
          stripe: {
            ...paymentDetails?.stripe,
            selectedCardId: cardId,
          },
        },
      })
    );
  };

  // function to handle stripe form display
  const handleToggleStripeForm = (): void => {
    // toggle form state
    setToggleStripeForm((prevState) => !prevState);

    // un-select the card if selected any from saved cards list
    if (paymentDetails?.stripe?.selectedCardId) {
      // set data in store
      dispatch(
        updateCheckoutStore({
          type: "paymentDetails",
          value: {
            ...paymentDetails,
            stripe: {
              ...paymentDetails?.stripe,
              selectedCardId: "",
            },
          },
        })
      );
    }
  };

  // ==============================|| UI ||============================== //

  // function to return the stripe saved cards if any
  const getStripeCardsUI = (): JSX.Element => {
    return (
      <>
        {cardsList?.length > 0 && (
          <>
            <Stack spacing={1.25}>
              <Item sx={labelStyles}>Saved cards</Item>

              <Box sx={{ flexGrow: 0 }}>
                <Grid container spacing={1.25}>
                  {cardsList?.map((card, index) => {
                    return (
                      <Grid item key={index} xs={12} md={6}>
                        <Card
                          key={index}
                          variant="outlined"
                          onClick={() => handleCardSelectionChange(card.id)}
                          sx={{
                            ...paymentCardsStyles,

                            borderColor:
                              paymentDetails?.stripe?.selectedCardId === card.id
                                ? "#000000"
                                : borderColor,
                          }}
                        >
                          <Typography
                            sx={{
                              ...tabContentTypoStyle,

                              fontSize: "14px",
                              color:
                                paymentDetails?.stripe?.selectedCardId ===
                                card.id
                                  ? "#000000"
                                  : "rgba(0, 0, 0, 0.50)",

                              fontWeight:
                                paymentDetails?.stripe?.selectedCardId ===
                                card.id
                                  ? 600
                                  : 400,
                            }}
                          >
                            {getCardLabel(card.last4)}
                          </Typography>

                          {cardIconsPath?.[card?.brand?.toUpperCase()] ? (
                            <CardMedia
                              component="img"
                              image={
                                cardIconsPath?.[card?.brand?.toUpperCase()] ||
                                ""
                              }
                              alt={card.brand || ""}
                              sx={paymentImageStyle}
                            />
                          ) : (
                            <Typography sx={tabContentTypoStyle}>
                              {card.brand}
                            </Typography>
                          )}
                        </Card>
                      </Grid>
                    );
                  })}
                </Grid>
              </Box>
            </Stack>

            <Button
              variant="text"
              startIcon={toggleStripeForm ? <DoNotDisturbOn /> : <AddCircle />}
              onClick={handleToggleStripeForm}
              sx={addCardBtnStyles}
            >
              Add new card
            </Button>
          </>
        )}
      </>
    );
  };

  return (
    <>
      {/* stripe saved cards UI */}
      {getStripeCardsUI()}

      {/* initially hide the stripe form if there is a saved card */}
      {(!cardsList?.length || toggleStripeForm) && (
        <Elements stripe={stripe}>
          <StripeForm />
        </Elements>
      )}
    </>
  );
};

export default Stripe;
