import { useState, useRef, useEffect, SetStateAction } from "react";

import {
  Grid,
  Box,
  List,
  ListItem,
  ListItemText,
  ClickAwayListener,
  Tooltip,
} from "@mui/material";
import { MyLocation } from "@mui/icons-material";
import { debounce } from "lodash";

import L, { LatLngExpression } from "leaflet";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ile<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  useMapEvents,
  useMap,
  ZoomControl,
} from "react-leaflet";
import "leaflet/dist/leaflet.css";
import icon from "leaflet/dist/images/marker-icon.png";
import iconShadow from "leaflet/dist/images/marker-shadow.png";
import { OpenStreetMapProvider } from "leaflet-geosearch";
import { SearchResult } from "leaflet-geosearch/dist/providers/provider";
import { RawResult } from "leaflet-geosearch/dist/providers/openStreetMapProvider";

import TextField from "components/textField";
import { mapContainer } from "checkout/checkoutForm/index.styles";
import { debouncePeriod, mapZoomLevel } from "utils/constant";
import { concatStrings, getCountryCode } from "utils/helperFunctions";

import { dispatch, useSelector } from "store";
import {
  bulkUpdateCheckoutStore,
  updateCheckoutStore,
} from "store/slices/checkout";

const LeafletMaps = () => {
  let map: L.Map | null = null;

  const [searchInput, setSearchInput] = useState<string>("");
  const [searchResults, setSearchResults] = useState<SearchResult<RawResult>[]>(
    []
  );

  const [error, setError] = useState<string>("");
  const [tooltipOpen, setTooltipOpen] = useState<boolean>(false);

  const [zoom, setZoom] = useState<number>(mapZoomLevel);

  const { userLocation, streetAddress, userArea, userCity, validationError } =
    useSelector((state) => state.checkout);
  const { branchDetails } = useSelector((state) => state.business);

  // marker icon
  const DefaultIcon = L.icon({
    iconUrl: icon,
    shadowUrl: iconShadow,
  });

  L.Marker.prototype.options.icon = DefaultIcon;

  const mapProvider = new OpenStreetMapProvider({
    params: {
      // limit search results to the branch country
      countrycodes: getCountryCode(branchDetails.country),
      addressdetails: 1,
    },
  });

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    if (userLocation.lat && userLocation.lng && streetAddress) {
      setSearchInput(concatStrings([streetAddress, userArea, userCity]));
    } else {
      getCurrentLocation();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // ==============================|| handler functions ||============================== //

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const currentPosition = {
            lat: position.coords.latitude,
            lng: position.coords.longitude,
          };

          if (map) {
            // Update the view to the current position
            map.setView(
              [currentPosition.lat, currentPosition.lng],
              map.getZoom()
            );
          }

          // Update the user location in the store
          dispatch(
            updateCheckoutStore({
              type: "userLocation",
              value: currentPosition,
            })
          );

          // Update the search results based on the current position
          setSearchResults([]);
          getAddress(currentPosition);
        },
        (error) => {
          setError(`${error.message}`);
          setTooltipOpen(true);

          // Close tooltip after 2 seconds
          setTimeout(handleTooltipClose, 2000);
        }
      );
    } else {
      alert("Geolocation is not supported by this browser.");
    }
  };

  // function to get user address details from lat lng
  const getAddress = async (coords: any) => {
    const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${coords.lat}&lon=${coords.lng}&zoom=13&addressdetails=1`;

    // fetch address details by providing lat lng
    fetch(url)
      .then((response) => response.json())
      .then((data) => {
        const addressDetails = data?.address;

        // set address details in store
        if (addressDetails) {
          setSearchInput(
            concatStrings([
              addressDetails.suburb || data.name || "",
              addressDetails?.city || addressDetails?.district || "",
            ])
          );

          dispatch(
            bulkUpdateCheckoutStore({
              type: "userAddressDetails",
              value: {
                userArea: addressDetails.suburb || data.name || "",
                userCountry: addressDetails?.country || "",
                userCity:
                  addressDetails?.city || addressDetails?.district || "",
                userCountryCode: addressDetails?.country_code || "",
                userPostalCode: addressDetails?.postcode || "",
                userState: addressDetails?.state || "",

                validationError: {
                  ...validationError,

                  userArea:
                    addressDetails.suburb || data.name
                      ? ""
                      : validationError.userArea,
                  userCity:
                    addressDetails?.city || addressDetails?.district
                      ? ""
                      : validationError.userCity,
                },
              },
            })
          );
        }
      })
      .catch((error) => console.error("Error:", error));
  };

  const handleAddressSearch = (event: {
    target: { value: SetStateAction<string> };
  }) => {
    setSearchInput(event.target.value);

    findSearchedAddress(event.target.value);
  };

  /**
   * using useRef() hook to store the debounced function
   * instead function will be recreated on every render of key stroke
   */
  const findSearchedAddress = useRef(
    debounce(async (address) => {
      try {
        const searchedResults = await mapProvider.search({ query: address });

        if (Array.isArray(searchedResults) && searchedResults?.length) {
          setSearchResults(searchedResults);
        } else {
          setSearchResults([]);
        }
      } catch (error) {
        dispatch(updateCheckoutStore({ type: "apiCallError", value: true }));
      }
    }, debouncePeriod)
  ).current;

  // function to set the position coordinates in store
  const setInitialCoordinates = () => {
    let initialPosition: LatLngExpression = [
      userLocation.lat || (branchDetails.lat ? Number(branchDetails.lat) : 0),
      userLocation.lng || (branchDetails.lng ? Number(branchDetails.lng) : 0),
    ];

    return initialPosition;
  };

  const MapMarker = () => {
    map = useMap();

    const mapE = useMapEvents({
      move: () => {
        const center = mapE.getCenter();

        // set user location in store
        dispatch(updateCheckoutStore({ type: "userLocation", value: center }));
      },

      dragend: () => {
        const center = mapE.getCenter();

        getAddress(center);
      },
      zoomend: () => {
        setSearchResults([]);

        setZoom(mapE.getZoom());
      },
    });

    return <Marker position={setInitialCoordinates()} />;
  };

  const handleTooltipClose = () => {
    setTooltipOpen(false);
  };

  // ==============================|| UI ||============================== //

  return (
    <Grid item sx={{ position: "relative", mt: "10px" }}>
      <Box
        sx={{
          position: "absolute",
          top: "0%",
          left: "0%",
          zIndex: "9999",
          p: "15px 15px",
          width: "100%",
        }}
      >
        <TextField
          name="searchAddress"
          placeholder="Enter delivery address"
          textTransform={"capitalize"}
          value={searchInput}
          onChange={handleAddressSearch}
        />

        {searchResults?.length > 0 && (
          <ClickAwayListener onClickAway={() => setSearchResults([])}>
            <List
              sx={{
                background: "#fff",
                p: "0",
                borderRadius: "6px",
                maxHeight: "250px",
                overflow: "auto",
              }}
            >
              {searchResults.map((result: any, index: number) => (
                <ListItem
                  key={index}
                  sx={{
                    p: "10px",
                    cursor: "pointer",

                    "& .MuiListItemText-primary": {
                      fontSize: "12px !important",
                      color: "#000 !important",
                    },
                  }}
                  onClick={() => {
                    if (map) {
                      map.setView([result.y, result.x], map.getZoom());
                    }
                    setSearchResults([]);

                    getAddress({ lat: result.y, lng: result.x });
                  }}
                >
                  <ListItemText primary={result.label} />
                </ListItem>
              ))}
            </List>
          </ClickAwayListener>
        )}
      </Box>

      <Box
        sx={{
          position: "absolute",
          bottom: "105px",
          right: "10px",
          zIndex: "9999",
          background: "#fff",
          borderRadius: "2px",
          border: "2px solid rgba(0, 0, 0, 0.2)",
          height: "40px",
          width: "33.5px",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          cursor: "pointer",
        }}
        onClick={getCurrentLocation}
      >
        <Tooltip open={tooltipOpen} onClose={handleTooltipClose} title={error}>
          <MyLocation
            sx={{ fontSize: "18.5px !important", color: "#000 !important" }}
          />
        </Tooltip>
      </Box>

      <MapContainer
        style={mapContainer}
        center={setInitialCoordinates()}
        zoom={zoom}
        zoomControl={false} // Disable the default zoom control
      >
        <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
        <MapMarker />

        <ZoomControl position="bottomright" />
      </MapContainer>
    </Grid>
  );
};

export default LeafletMaps;
