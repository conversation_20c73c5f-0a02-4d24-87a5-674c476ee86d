import { useCallback } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { paymentMethods } from "utils/constant";
import { paymentMethodFailureMessage } from "utils/helperFunctions";
import { onlinePayment } from "utils/apiConfig";
import { dispatch, useSelector } from "store";
import { bulkUpdateCheckoutStore } from "store/slices/checkout";

export const useAlfalahPayment = () => {
  const { businessId, paymentDetails, checkoutProcessState } = useSelector(
    (state) => state.checkout
  );

  // ==============================|| API ||============================== //

  // API call to get bank alfalah payment session
  const [{ data, loading, error }, getAlfalahSessionAPICall] = useAxios(
    onlinePayment(businessId, paymentMethods?.BANK_ALFALAH?.value),
    { manual: true }
  );

  // ==============================|| handler functions ||============================== //

  // function to get alfalah payment session
  const createAlfalahSession = useCallback(
    async (config: AxiosRequestConfig, orderId: number): Promise<void> => {
      try {
        // API call to get alfalah session
        const { data } = await getAlfalahSessionAPICall(config);

        // verify API response
        if (data?.status === 200 && data?.result?.session?.id) {
          // set session id & order id in store to load payment method
          dispatch(
            bulkUpdateCheckoutStore({
              type: "",
              value: {
                checkoutProcessState: { ...checkoutProcessState, id: orderId },
                paymentDetails: {
                  ...paymentDetails,
                  alfalahSessionId: data?.result?.session?.id,
                },
              },
            })
          );
        } else {
          // set the payment method failure message for user
          paymentMethodFailureMessage(orderId);
        }
      } catch (error) {
        // set the payment method failure message for user
        paymentMethodFailureMessage(orderId);
      }
    },

    // eslint-disable-next-line react-hooks/exhaustive-deps
    [getAlfalahSessionAPICall]
  );

  return {
    data,
    loading,
    error,
    getAlfalahSessionAPICall: createAlfalahSession,
  };
};
