import { useCallback } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { paymentMethods } from "utils/constant";
import { onlinePayment } from "utils/apiConfig";
import { useSelector } from "store";

export const useStripePayment = () => {
  const { businessId } = useSelector((state) => state.checkout);

  // ==============================|| API ||============================== //

  // API call to trigger stripe to charge payment
  const [{ data, loading, error }, stripePaymentChargeAPICall] = useAxios(
    onlinePayment(businessId, paymentMethods?.STRIPE?.value),
    { manual: true }
  );

  // ==============================|| handler functions ||============================== //

  // function to get payMob payment token
  const chargeStripePayment = useCallback(
    async (config: AxiosRequestConfig): Promise<boolean> => {
      try {
        // API call to charge payment
        const { data } = await stripePaymentChargeAPICall(config);

        // returns boolean on verifying the API response
        return data?.status === 200;
      } catch (error) {
        return false;
      }
    },
    [stripePaymentChargeAPICall]
  );

  return {
    data,
    loading,
    error,
    stripePaymentChargeAPICall: chargeStripePayment,
  };
};
