import { createSlice } from "@reduxjs/toolkit";
import { PaymentMethodType } from "checkout/index.types";
import { CheckoutSettings } from "types";

export interface BusinessTypes {
  businessDetails: {
    name: string;
    currencyCode: string;
    decimalPlaces: number;
    paymentMethods: PaymentMethodType[];
    futureOrder: number;
    tip: {
      type: number;
      label: string;
      value: number;
      pickup: boolean;
      enabled: boolean;
      delivery: boolean;

      [key: string]: string | number | boolean;
    };
    googleKey: string;
    checkoutSettings: CheckoutSettings;
  };
  branchDetails: {
    address: string;
    country: string;
    city: string;
    area: string;
    lat: string;
    lng: string;
    deliverySettings: any;
    timeZone: string;
    minSpend: number;
  };

  [key: string]: any;
}

// initial state
const initialState: BusinessTypes = {
  businessDetails: {
    name: "",
    currencyCode: "",
    decimalPlaces: 0,
    paymentMethods: [],
    futureOrder: 0,
    tip: {
      type: 0,
      label: "",
      value: 0,
      pickup: false,
      enabled: false,
      delivery: false,
    },
    googleKey: "",
    checkoutSettings: {
      submitFormButtonText: "Place Order",
      forceUserAuth: false,
    },
  },
  branchDetails: {
    address: "",
    country: "",
    city: "",
    area: "",
    lat: "",
    lng: "",
    deliverySettings: null,
    timeZone: "",
    minSpend: 0,
  },
};

const Business = createSlice({
  name: "business",
  initialState,
  reducers: {
    updateBusinessStore(state, action) {
      state[action.payload.type] = action.payload.value;
    },
  },
});

export default Business.reducer;

export const { updateBusinessStore } = Business.actions;
