import { useEffect } from "react";
import {
  createRoutesFromChildren,
  matchRoutes,
  useLocation,
  useNavigationType,
} from "react-router-dom";

import * as Sentry from "@sentry/react";

Sentry.init({
  dsn: "https://<EMAIL>/4506687284183040",
  environment: process.env.REACT_APP_ENV || "development",
  integrations: [
    Sentry.reactRouterV6BrowserTracingIntegration({
      useEffect,
      useLocation,
      useNavigationType,
      createRoutesFromChildren,
      matchRoutes,
    }),
    Sentry.replayIntegration(),
  ],

  tracesSampleRate: 0.1,

  // TODO: Required later
  // // set endpoints to control for which URLs trace propagation should be enabled
  // tracePropagationTargets: [
  //   /^\//,
  //   new RegExp(process.env.REACT_APP_BASEURL!),
  //   new RegExp(process.env.REACT_APP_CART!),
  //   new RegExp(process.env.REACT_APP_BUSINESS!),
  //   new RegExp(process.env.REACT_APP_SUBSCRIPTION!),
  // ],

  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 0.1,
});
