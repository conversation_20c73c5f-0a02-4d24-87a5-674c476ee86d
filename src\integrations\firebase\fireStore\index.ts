import moment from "moment-timezone";

import { GenericMap } from "types";
import {
  environments,
  firebaseCollections,
  dateTimeFormats,
} from "utils/constant";
import {
  collection,
  doc,
  DocumentData,
  DocumentReference,
  serverTimestamp,
  setDoc,
  updateDoc,
} from "firebase/firestore";
import { fireStoreDB } from "integrations/firebase";
import { store } from "store";

// function to log API calls
export const logAPICall = (
  logData: GenericMap,
  docId: string,
  updateLog: boolean
): void => {
  // get Redux state
  const state = store?.getState();

  /**
   * log API calls when
   * => in production environment
   * => document id is available
   */
  if (process.env.REACT_APP_NODE_ENV !== environments?.PRODUCTION || !docId) {
    return;
  }

  try {
    // get the document reference
    const firebaseDocument: DocumentReference<DocumentData, DocumentData> = doc(
      collection(fireStoreDB, firebaseCollections?.LOGS),
      docId
    );

    // calculate document expiry
    const documentExpiry: string = moment()
      ?.utc()
      ?.add(state?.firebase?.settings?.expireLogs, "days")
      ?.format(dateTimeFormats.DATE_TIME);

    // check if updateLog is true or false
    if (updateLog) {
      // for response & error, use updateDoc to update the document
      updateDoc(firebaseDocument, {
        ...logData,
        responseTimestamp: serverTimestamp(),
      });
    } else {
      // for requests, use setDoc to create new document
      setDoc(firebaseDocument, {
        ...logData,
        requestTimestamp: serverTimestamp(),
        expiry: documentExpiry,
      });
    }
  } catch (error) {
    console.error(`Error logging API: `, error);
  }
};

// function to log GA4 events
export const logGA4Data = (logData: GenericMap): void => {
  // generate time stamp for document id
  const documentId: string = moment()
    ?.utc()
    ?.format(dateTimeFormats?.DATE_TIME_MILLISECONDS);

  /**
   * log API calls when
   * => in production environment
   * => document id is available
   */
  if (process.env.REACT_APP_NODE_ENV !== environments?.PRODUCTION) {
    return;
  }

  try {
    // get the document reference
    const firebaseDocument: DocumentReference<DocumentData, DocumentData> = doc(
      collection(fireStoreDB, firebaseCollections?.GA4),
      documentId
    );

    // for requests, use setDoc to create new document
    setDoc(firebaseDocument, { ...logData });
  } catch (error) {
    console.error(`Error logging events: `, error);
  }
};
