import { SyntheticEvent } from "react";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Fade,
  Grow,
  IconButton,
  Slide,
  SlideProps,
} from "@mui/material";
import MuiSnackbar from "@mui/material/Snackbar";
import CloseIcon from "@mui/icons-material/Close";

import { alertStyles } from "./index.styles";
import { GenericMap } from "types";
import { useDispatch, useSelector } from "store";
import { closeSnackbar } from "store/slices/snackbar";

// animation function
function TransitionSlideLeft(props: SlideProps) {
  return <Slide {...props} direction="left" />;
}

function TransitionSlideUp(props: SlideProps) {
  return <Slide {...props} direction="up" />;
}

function TransitionSlideRight(props: SlideProps) {
  return <Slide {...props} direction="right" />;
}

function TransitionSlideDown(props: SlideProps) {
  return <Slide {...props} direction="down" />;
}

function GrowTransition(props: SlideProps) {
  return <Grow {...props} />;
}

// animation options
const animation: GenericMap = {
  SlideLeft: TransitionSlideLeft,
  SlideUp: TransitionSlideUp,
  SlideRight: TransitionSlideRight,
  SlideDown: TransitionSlideDown,
  Grow: GrowTransition,
  Fade,
};

// ==============================|| SNACKBAR ||============================== //

const Snackbar = () => {
  const dispatch = useDispatch();
  const snackbar = useSelector((state) => state.snackbar);
  const {
    actionButton,
    anchorOrigin,
    alert,
    close,
    message,
    open,
    keepItOpen,
    transition,
  } = snackbar;

  const handleClose = (event: SyntheticEvent | Event, reason?: string) => {
    if (reason === "clickaway") {
      return;
    }

    dispatch(closeSnackbar());
  };

  return (
    <MuiSnackbar
      TransitionComponent={animation[transition]}
      anchorOrigin={anchorOrigin}
      open={open}
      autoHideDuration={keepItOpen ? 15000 : 2500}
      onClose={handleClose}
    >
      <Alert
        variant={alert.variant}
        color={alert.color}
        action={
          <>
            {actionButton !== false && (
              <Button
                size="small"
                onClick={handleClose}
                sx={{ color: "background.paper" }}
              >
                UNDO
              </Button>
            )}

            {close !== false && (
              <IconButton
                sx={{ color: "background.paper" }}
                size="small"
                aria-label="close"
                onClick={handleClose}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            )}
          </>
        }
        sx={alertStyles}
      >
        {message}
      </Alert>
    </MuiSnackbar>
  );
};

export default Snackbar;
