import { useCallback, useEffect, useRef } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { StripeCustomerInfo } from "checkout/index.types";
import { paymentMethods } from "utils/constant";
import { stripeCustomerRegistration } from "utils/apiConfig";
import { setDisableComponentFlag } from "utils/helperFunctions";
import { useSelector } from "store";

export const useRegisterStripeCustomer = () => {
  let customerDetails = useRef<StripeCustomerInfo>({
    customer_id: "",
    card_id: "",
  });

  const { businessId } = useSelector((state) => state.checkout);

  // ==============================|| API ||============================== //

  // API call to register stripe customer
  const [{ data, loading, error }, registerStripeCustomerAPICall] = useAxios(
    stripeCustomerRegistration(businessId, paymentMethods?.STRIPE?.value),
    { manual: true }
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function to set the API loading state in store to disable components
    setDisableComponentFlag(loading);
  }, [loading]);

  // ==============================|| handler functions ||============================== //

  // function to call stripe customer registration API
  const registerStripeCustomer = useCallback(
    async (config: AxiosRequestConfig): Promise<StripeCustomerInfo> => {
      try {
        // API call to register a customer on stripe
        const { data } = await registerStripeCustomerAPICall(config);

        // verify API response
        if (
          data?.status === 200 &&
          data.result?.customer_id &&
          data.result?.card_id
        ) {
          // update customer details in state
          customerDetails.current = {
            customer_id: data.result?.customer_id,
            card_id: data.result?.card_id,
          };

          return customerDetails.current;
        }

        return customerDetails.current;
      } catch (error) {
        return customerDetails.current;
      }
    },
    [registerStripeCustomerAPICall]
  );

  return {
    data,
    loading,
    error,
    registerStripeCustomerAPICall: registerStripeCustomer,
  };
};
