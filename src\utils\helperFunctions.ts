import { SxProps, Theme } from "@mui/material";
import moment from "moment-timezone";
import Cookies from "js-cookie";
import parsePhoneNumberFromString from "libphonenumber-js";
import * as Sentry from "@sentry/react";

import { paymentRadioStyles } from "checkout/checkoutForm/index.styles";
import {
  SubscriptionType,
  SubscriptionDetails,
  SubscriptionSchedule,
  UserSavedData,
  PaymentMethodType,
  SelectedSubscriptionInfo,
  OrderValidation,
} from "checkout/index.types";
import { AuthValidation } from "checkout/manageAccount/index.types";
import {
  OrderScheduleType,
  CartItemsType,
  GenericMap,
  BusinessInfoType,
  DaySuffix,
  PaymentOptions,
  CartDetailsType,
  BranchCartSettings,
  BusinessCheckoutSettings,
  CheckoutSettings,
} from "types";
import {
  orderSuccess,
  orderTypes,
  paymentMethodFailure,
  subscriptionSuccess,
  subscriptionSuccessMessage,
  transactionSuccess,
  userAuthTypes,
  emailValidationPattern,
  dateTimeFormats,
  cardPlaceholder,
  pkCurrencyCode,
  paymentTypes,
  orderSuccessMessage,
  transactionFailure,
  transactionFailureMessage,
  pageNotFound,
  pageErrorMessage,
  dataFailure,
  dataFailureMessage,
  subscriptionTypes,
  UIElementIds,
  sourceTypes,
  socialPlatforms,
  paymentMethods,
  stripeMinSpend,
  cookieOptions,
} from "./constant";
import { logGA4Data } from "integrations/firebase/fireStore";
import { dispatch } from "store";
import { CheckoutTypes, updateCheckoutStore } from "store/slices/checkout";
import { BusinessTypes } from "store/slices/business";
import { openSnackbar } from "store/slices/snackbar";

// returns day against a date
export const formatDay = (date: string, timeZoneOffset: string) => {
  // TODO: may required later
  // const today = convertTimeZoneOffset(timeZoneOffset);
  // const tomorrow = moment(today).add(1, "days").format("YYYY-MM-DD");

  // if (date === today) {
  //   return "Today";
  // } else if (date === tomorrow) {
  //   return "Tomorrow";
  // } else {
  //   // return day name for other dates
  //   return moment(date).format("dddd");
  // }

  return moment(date)?.format("dddd");
};

// return string having capitalize first letter of every word
export const capitalizeFirstLetter = (inputString: string) => {
  return inputString?.replace(/\b\w/g, (char) => char.toUpperCase());
};

// function to concat the strings
export const concatStrings = (strings: string[]) => {
  // filter out empty strings and join them with a comma
  return strings.filter((str) => str).join(", ");
};

// get the user location from browser and update in store
export const getUserPosition = async () => {
  if ("geolocation" in navigator) {
    try {
      const geoPosition: GeolocationPosition = await new Promise(
        (resolve, reject) => {
          navigator.geolocation.getCurrentPosition(
            resolve,
            reject,

            {
              enableHighAccuracy: true,
              timeout: 5000, // timeout in milliseconds
              maximumAge: 0, // maximum age of cached position
            }
          );
        }
      );

      const { latitude, longitude } = geoPosition.coords;

      // set user location in store
      await dispatch(
        updateCheckoutStore({
          type: "userLocation",
          value: { lat: latitude, lng: longitude },
        })
      );
    } catch (error) {}
  } else {
    alert("Geolocation is not supported by this browser");
  }
};

/**
 * function to format the price with currency symbol & decimal places
 * regex to add space between currency & amount
 */
export const getFormattedPrice = (
  price: number,
  currency: string,
  decimalPlaces: number
): string => {
  return new Intl.NumberFormat("en-PK", {
    style: "currency",
    currencyDisplay: "symbol",
    currency: currency,
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces,
  })
    ?.format(price || 0)
    ?.replace(/(\D)(\d)/, (_, currency, amount) => `${currency}${amount}`);
};

// function to calculate the value of certain percentage
export const calculatePercentageValue = (
  percentage: number,
  total: number,
  decimalPlaces = 0
) => {
  const value = (percentage * total) / 100;

  return value ? value.toFixed(decimalPlaces) : (0).toFixed(decimalPlaces);
};

// clear local storage & cookies on order placement
export const clearStorage = () => {
  localStorage.removeItem("cartData");
  localStorage.removeItem("cart");

  // removing these keys for ai-project
  localStorage.removeItem("boxSize");
  localStorage.removeItem("orderType");
  localStorage.removeItem("ezeats-delivery-timing");
  localStorage.removeItem("deliveryAddress");
  localStorage.removeItem("addressDetails");
  localStorage.removeItem("userLocation");
  localStorage.removeItem("scheduledDateTime");
  localStorage.removeItem("orderSchedule");
  localStorage.removeItem("unique_order_id");

  Cookies.remove("unique_order_id");
  Cookies.remove("total_qty");
  Cookies.remove("date");
  Cookies.remove("Time");
};

// function to return the country code
export const getCountryCode = (country: string) => {
  if (country.toLowerCase() === "pakistan") {
    return "pk";
  } else if (country.toLowerCase() === "canada") {
    return "ca";
  } else if (country.toLowerCase() === "usa") {
    return "us";
  }

  return "";
};

// function to set the subscription successful message for user
export const subscriptionMessage = (id: number): void => {
  dispatch(
    updateCheckoutStore({
      type: "checkoutProcessState",
      value: {
        id,
        successFlag: true,
        errorFlag: false,
        errorCode: 404,
        statusMessage: subscriptionSuccess,
        message: subscriptionSuccessMessage,
        reloadPaymentMethod: false,
      },
    })
  );
};

// function to set the successful message for user
export const cashPaymentMessage = (id: number): void => {
  dispatch(
    updateCheckoutStore({
      type: "checkoutProcessState",
      value: {
        id,
        successFlag: true,
        errorFlag: false,
        errorCode: 404,
        statusMessage: orderSuccess,
        message: orderSuccessMessage,
        reloadPaymentMethod: false,
      },
    })
  );
};

// function to set the online payment success message for user
export const paymentSuccessMessage = (id: number): void => {
  dispatch(
    updateCheckoutStore({
      type: "checkoutProcessState",
      value: {
        id,
        successFlag: true,
        errorFlag: false,
        errorCode: 404,
        statusMessage: transactionSuccess,
        message: orderSuccessMessage,
        reloadPaymentMethod: false,
      },
    })
  );
};

// function to set the online payment failure message for user
export const paymentFailureMessage = (id: number): void => {
  dispatch(
    updateCheckoutStore({
      type: "checkoutProcessState",
      value: {
        id,
        successFlag: true,
        errorFlag: false,
        errorCode: 404,
        statusMessage: transactionFailure,
        message: transactionFailureMessage,
        reloadPaymentMethod: false,
      },
    })
  );
};

// function to set the message for user when failed to load payment method
export const paymentMethodFailureMessage = (id: number): void => {
  dispatch(
    updateCheckoutStore({
      type: "checkoutProcessState",
      value: {
        id,
        successFlag: true,
        errorFlag: false,
        errorCode: 404,
        statusMessage: orderSuccess,
        message: paymentMethodFailure,
        reloadPaymentMethod: true,
      },
    })
  );
};

// function to set the message for user when failed to get pre checkout data
export const preCheckoutFailureMessage = (): void => {
  dispatch(
    updateCheckoutStore({
      type: "checkoutProcessState",
      value: {
        id: 0,
        successFlag: false,
        errorFlag: true,
        errorCode: 404,
        statusMessage: pageNotFound,
        message: pageErrorMessage,
        reloadPaymentMethod: false,
      },
    })
  );
};

// function to set the message for user on business/cart api failure
export const apiFailureMessage = (): void => {
  dispatch(
    updateCheckoutStore({
      type: "checkoutProcessState",
      value: {
        id: 0,
        successFlag: false,
        errorFlag: true,
        errorCode: 500,
        statusMessage: dataFailure,
        message: dataFailureMessage,
        reloadPaymentMethod: false,
      },
    })
  );
};

// function to redirect to the custom confirmation page
export const handleCustomConfirmationPage = (
  redirectURL: string,
  orderId: number,
  checkoutDetails: CheckoutTypes
): void => {
  // set order details to cookies to display on custom page
  Cookies.set(
    "confirmationPageDetails",
    JSON.stringify({
      orderId: orderId?.toString(),
      orderType: checkoutDetails?.orderType,
      orderSchedule: checkoutDetails?.orderSchedule,
      subscription: {
        type: checkoutDetails?.subscription?.type,
        schedule: formatSubscriptionSchedule(checkoutDetails?.subscription),
      },
    }),
    cookieOptions
  );

  // redirect to the custom page
  window.location.replace(redirectURL);
};

// return the color for headings to indicate error if any
export const getLabelColor = (value: boolean) => {
  return value ? "#FF0238" : "#000";
};

// return first 3 letters of postal code that represents area
export const getPostalArea = (postalCode: string) => {
  return postalCode.length > 3 ? postalCode.slice(0, 3) : postalCode;
};

// extract base URL
export const getSiteUrl = () => {
  const url = new URL(window.location.href);

  return `${url.protocol}//${url.hostname}`;
};

// returns boolean on validating data retrieved from cross technology
export const validateBusinessInfo = (
  businessInfo: BusinessInfoType
): string => {
  return (
    businessInfo?.cartId &&
    businessInfo?.businessId &&
    businessInfo?.branchId &&
    businessInfo?.source
  );
};

// function to handle the back navigation based on parent source type
export const handleBackNavigation = (source: string, siteUrl: string): void => {
  // check if the source is from the app
  if (source === sourceTypes?.APP) {
    // capture the React Native webView component and post message to return to the menu
    window?.ReactNativeWebView?.postMessage(
      JSON.stringify({ action: "RETURN_TO_MENU" })
    );
  } else {
    // navigate back to the site
    window.location.replace(siteUrl);
  }
};

// return label for Cash payment according to order type
export const getPaymentLabel = (
  paymentOption: PaymentMethodType,
  orderType: string
): string => {
  return paymentOption?.type === paymentTypes?.COD?.label
    ? `${paymentOption?.label} on ${orderType}`
    : paymentOption?.label;
};

// return label for payment card numbers
export const getCardLabel = (cardNumber: string): string => {
  return `${cardPlaceholder} ${cardNumber}`;
};

// function to return the payment method box styles
export const getRadioStyles = (selected: boolean) => {
  return {
    ...paymentRadioStyles,

    borderColor: selected ? "#141414" : "#EEEEEE",
  };
};

// function to return time according to provided time zone offset
export const convertTimeZoneOffset = (
  timeZoneOffset: string,
  format = dateTimeFormats?.DATE
): string => {
  let currentDate: string = moment()?.format(format);

  // get the current date according to the time zone if exists
  if (timeZoneOffset) {
    currentDate = moment
      ?.utc()
      ?.clone()
      ?.utcOffset(timeZoneOffset)
      ?.format(format);
  }

  return currentDate;
};

// comparison of current time for future ordering
export const compareCurrentTime = (
  orderSchedule: OrderScheduleType,
  timeZoneOffset: string
): boolean => {
  /**
   * create UTC moment object of provided date and time (converting to UTC to avoid comparison failure in iOS)
   * true => strict parsing (returns invalid date if format mismatch)
   */
  const orderDateTime = moment?.utc(
    `${orderSchedule?.date} ${orderSchedule?.time}`,
    `${dateTimeFormats?.DATE} ${dateTimeFormats?.MINUTES_TIME_MERIDIAN}`,
    true
  );

  // get current time according to timezone offset and convert to UTC for comparison
  const currentTime = moment.utc(
    convertTimeZoneOffset(
      timeZoneOffset,
      `${dateTimeFormats?.DATE} ${dateTimeFormats?.TIME}`
    ),
    `${dateTimeFormats?.DATE} ${dateTimeFormats?.TIME}`
  );

  // returns boolean according to date validity & comparison
  return orderDateTime?.isValid() && orderDateTime?.isAfter(currentTime);
};

// returns the return url for payment methods
export const getReturnURL = (
  siteUrl: string,
  paymentMethod: string,
  orderId: number,
  paymentId: number
): string => {
  return `${siteUrl}/order/payment-confirmation/${paymentMethod}?order_id=${orderId}&eatout_uid=${paymentId}`;
};

// returns the return url for alfalah payment method
export const getAlfalahReturnURL = (
  siteUrl: string,
  orderId: number,
  paymentId: number
): string => {
  return `${siteUrl}/website/placeorder/${orderId}?eatout_uid=${paymentId}`;
};

// returns the alfalah payment method script attributes
export const getAlfalahScriptAttributes = (
  attribute: string,
  siteUrl: string,
  orderId: number
): string => {
  return attribute === "error"
    ? `${siteUrl}/order/error?trnApproved=0&gateway=bankalfalah&trnOrderNumber=${orderId}`
    : `${siteUrl}/order/error?trnApproved=0&gateway=bankalfalah&cancelOrderUser=1&trnOrderNumber=${orderId}`;
};

// function to return the payMob payment page url
export const getPayMobPageURL = (
  accountId: string,
  payMobToken: string
): string => {
  return `https://pakistan.paymob.com/api/acceptance/iframes/${accountId}?payment_token=${payMobToken}`;
};

// function to return the JSON for subscription API payload
export const getSubscriptionScheduleJSON = (
  subscription: SubscriptionType
): SubscriptionSchedule => {
  if (subscription?.type === "one-time") {
    const formattedTime: string = moment(subscription?.time).format(
      dateTimeFormats?.DATE_TIME
    );

    return formattedTime;
  } else {
    const scheduleJSON: { [key: string]: string } = {};

    const time: string = moment(subscription.time, [
      dateTimeFormats?.MINUTES_TIME_MERIDIAN,
      dateTimeFormats?.TIME,
    ]).format(dateTimeFormats?.TIME);

    for (const key in subscription.details) {
      if (subscription.details[key].selected) {
        scheduleJSON[key] = time;
      }
    }

    return scheduleJSON;
  }
};

// function to generate the initial structure for subscription
export const generateSubscriptionStructure = (
  type: string
): SubscriptionDetails => {
  const daysInMonth: number = moment().daysInMonth();
  const subscriptionDetails: SubscriptionDetails = {};

  // monthly (dates) subscription
  if (type === subscriptionTypes?.MONTHLY) {
    for (let dayNo = 1; dayNo <= daysInMonth; dayNo++) {
      const daySuffix: DaySuffix = getDaySuffix(dayNo);

      // update JSON
      subscriptionDetails[dayNo] = {
        day: `${dayNo}${daySuffix}`,
        selected: false,
      };
    }
  } else {
    // weekly/bi-weekly (days) subscription
    for (let dayNo = 0; dayNo < 7; dayNo++) {
      const dayName: string = moment().day(dayNo).format("dddd");

      // update JSON
      subscriptionDetails[dayNo] = { day: dayName, selected: false };
    }
  }

  return subscriptionDetails;
};

// returns the suffix for the numbers
export const getDaySuffix = (day: number): DaySuffix => {
  if (day >= 11 && day <= 13) return "th";

  switch (day % 10) {
    case 1:
      return "st";
    case 2:
      return "nd";
    case 3:
      return "rd";
    default:
      return "th";
  }
};

// set current day/date as selected for subscription
export const setInitialSubscription = (
  subscriptionType: string,
  timezoneOffset: string,
  orderSchedule: OrderScheduleType
): SelectedSubscriptionInfo => {
  // function to generate initial JSON structure
  const subscriptionDetails: SubscriptionDetails =
    generateSubscriptionStructure(subscriptionType);

  // use order schedule date if exists, otherwise use current date
  const currentDate: moment.Moment = orderSchedule?.date
    ? moment(orderSchedule?.date)
    : moment().utcOffset(timezoneOffset);
  let subscriptionKey: string = "";

  // monthly (dates) subscription
  if (subscriptionType === subscriptionTypes?.MONTHLY) {
    // returns date as a number (1-31)
    subscriptionKey = currentDate?.date()?.toString();
  } else {
    // weekly/bi-weekly (days) subscription

    // returns day as a number (0-6) => (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
    subscriptionKey = currentDate?.day()?.toString();
  }

  // set current day/date as selected in subscription details
  subscriptionDetails[subscriptionKey].selected = true;

  // return subscription details and key
  return { subscriptionDetails, subscriptionKey };
};

// generate date from subscription day/date to call pickup hours API
export const generateSubscriptionDate = (
  subscription: SubscriptionType
): string => {
  // find the first (selected: true) entry
  const selectedEntry = Object.entries(subscription?.details || {}).find(
    ([, detail]) => detail.selected
  );

  let subscriptionDate: string = "";

  if (selectedEntry) {
    // destructure the subscription day/date key
    const [subscriptionKey] = selectedEntry;

    // monthly (dates) subscription
    if (subscription?.type === subscriptionTypes?.MONTHLY) {
      // get the current month (0-11) and year
      const currentMonth: number = moment().month();
      const currentYear: number = moment().year();

      // determine the date of the current month using the selected subscription date offset
      let selectedDate: moment.Moment = moment(
        `${currentYear}-${currentMonth + 1}-${subscriptionKey}`,
        dateTimeFormats?.DATE
      );

      // if the selected subscription date is in the past, move to the next month
      if (selectedDate.isBefore(moment(), "day")) {
        selectedDate = selectedDate.add(1, "month");
      }

      // format the date
      subscriptionDate = selectedDate.format(dateTimeFormats?.DATE);
    } else {
      // weekly/bi-weekly (days) subscription

      // get today's day index (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
      const dayOffset: number = moment().day();

      /**
       * calculate the number of days until the next occurrence of the selected day
       * doing so to get the date of the upcoming day if it has been passed
       */
      const daysUntilNext: number =
        (Number(subscriptionKey) + 7 - dayOffset) % 7;

      // get the date for the upcoming selected day
      subscriptionDate = moment()
        .add(daysUntilNext, "days")
        .format(dateTimeFormats?.DATE);
    }
  }

  return subscriptionDate;
};

// format the selected subscription schedule to display for user
export const formatSubscriptionSchedule = (
  subscription: SubscriptionType
): string => {
  // filter out the selected days
  const selectedDays: string[] = Object.values(subscription?.details)?.flatMap(
    (detail) => {
      if (detail?.selected) {
        // add 's' to make day names plural for weekly/bi-weekly subscriptions
        const dayName: string =
          subscription?.type === subscriptionTypes?.WEEKLY ||
          subscription?.type === subscriptionTypes?.BI_WEEKLY
            ? `${detail?.day}s`
            : detail?.day;

        return dayName;
      }

      return [];
    }
  );

  // return the formatted schedule
  return selectedDays?.length
    ? `${selectedDays?.join(", ")} - ${subscription.time}`
    : "";
};

// format the order schedule
export const formatOrderSchedule = (
  orderSchedule: OrderScheduleType,
  timeZone: string
): string => {
  return orderSchedule?.time
    ? `${formatDay(orderSchedule?.date, timeZone)} - ${orderSchedule?.time}`
    : `${formatDay(orderSchedule?.date, timeZone)}`;
};

// validates the data before posting a new order
export const orderValidation = (
  checkoutDetails: CheckoutTypes,
  businessDetails: BusinessTypes["businessDetails"],
  branchDetails: BusinessTypes["branchDetails"]
): boolean => {
  // verify cart items
  if (!checkoutDetails?.cartDetails?.items?.length) {
    // function to set message to notify user
    userNotification(
      "No item found in cart! Add items to cart to place order."
    );

    return false;
  }

  // verify cart minimum spend
  if (checkoutDetails?.cartDetails?.total < branchDetails?.minSpend) {
    // function to set message to notify user
    userNotification(
      `Spend at least ${getFormattedPrice(
        branchDetails?.minSpend,
        businessDetails?.currencyCode,
        businessDetails?.decimalPlaces
      )} to place order!`
    );

    return false;
  }

  // verify min amount stripe processes
  if (
    checkoutDetails?.paymentType?.type === paymentMethods?.STRIPE?.value &&
    checkoutDetails?.cartDetails?.gtotal < stripeMinSpend
  ) {
    // function to set message to notify user
    userNotification(
      `Threshold amount must be greater than ${getFormattedPrice(
        stripeMinSpend,
        businessDetails?.currencyCode,
        businessDetails?.decimalPlaces
      )}`
    );

    return false;
  }

  // sets the key true if condition fails
  const dataValidation: OrderValidation = {
    ...checkoutDetails?.validationError,

    name: !checkoutDetails?.name?.trim(),
    email: validateEmail(checkoutDetails?.email?.trim()),
    phone: !validatePhoneNumber(checkoutDetails?.phone),
    deliveryAddress:
      ((!checkoutDetails?.address ||
        !checkoutDetails?.userCity ||
        !checkoutDetails?.userArea) &&
        checkoutDetails?.orderType === orderTypes?.DELIVERY) ||
      checkoutDetails?.userLocationFlag,

    /**
     * disable schedule validation if subscription enabled
     * order schedule => verify date/time existence & compare them with current time
     */
    orderSchedule: checkoutDetails?.subscription?.type
      ? false
      : !(
          checkoutDetails?.orderSchedule?.date &&
          checkoutDetails?.orderSchedule?.time &&
          compareCurrentTime(
            {
              date: checkoutDetails?.orderSchedule?.date,
              time: checkoutDetails?.orderSchedule?.time,
            },
            branchDetails?.timeZone
          )
        ) &&
        (checkoutDetails?.orderType === orderTypes?.PICK_UP ||
          (checkoutDetails?.orderType === orderTypes?.DELIVERY &&
            businessDetails?.futureOrder)),

    // subscription validation
    subscription: checkoutDetails?.subscription?.type
      ? !validateSubscription(checkoutDetails?.subscription)
      : false,
  };

  // find the first key with value true
  const invalidKey: string | undefined = Object.keys(dataValidation)?.find(
    (key) => dataValidation[key]
  );

  // validate if any error found
  if (invalidKey) {
    // set data in store to reflect errors if any
    dispatch(
      updateCheckoutStore({ type: "validationError", value: dataValidation })
    );

    // function call to scroll window to specified element
    scrollToElement(UIElementIds[invalidKey]);

    return false;
  }

  return true;
};

// validates the required values for order schedule
export const validateOrderSchedule = (
  orderSchedule: OrderScheduleType
): boolean => {
  // return boolean according to order schedule data
  return Boolean(orderSchedule?.date && orderSchedule?.time);
};

// validates the required values for subscription
export const validateSubscription = (
  subscription: SubscriptionType
): boolean => {
  // return true if time is set and at least one day is selected
  return Boolean(
    subscription?.time &&
      Object.values(subscription?.details)?.some((value) => value?.selected)
  );
};

// function to scroll window to specified element
export const scrollToElement = (elementId: string): void => {
  // get element
  const element: HTMLElement | null = document?.getElementById(elementId);

  // verify element
  if (element) {
    // scroll to the element
    element?.scrollIntoView({ behavior: "smooth", block: "center" });
  }
};

// function to scroll window to bottom
export const scrollToBottom = (
  selectedPaymentMethod: PaymentMethodType,
  paymentMethodsList: PaymentMethodType[],
  loadingFlag: boolean
): void => {
  /**
   * scroll to bottom if selected payment method is bank transfer or stripe
   * and if there are multiple payment methods available
   * and if the loading flag is false
   */
  if (
    (selectedPaymentMethod?.type === paymentMethods?.BANK_TRANSFER?.value ||
      selectedPaymentMethod?.type === paymentMethods?.STRIPE?.value) &&
    paymentMethodsList?.length > 1 &&
    !loadingFlag
  ) {
    window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" });
  }
};

// validates auth data
export const authValidation = (data: AuthValidation): boolean => {
  // checks if any of the error is true
  return Object.values(data)?.some((value) => value);
};

// function to get items count of an order
export const orderItemsCount = (orderItems: CartItemsType[]): number => {
  const totalQuantity: number = orderItems.reduce(
    (sum, item) => sum + item.dqty,
    0
  );

  return totalQuantity;
};

// function to return tab index based on value
export const getTabValue = (selectedAuthTab: string): number => {
  return selectedAuthTab === userAuthTypes?.LOGIN ? 0 : 1;
};

// function to generate dates for order schedule
export const generateScheduleDates = (timeZoneOffset: string): string[] => {
  // get the current date according to the time zone
  const formattedStartDate: string = convertTimeZoneOffset(timeZoneOffset);
  const startDate: moment.Moment = moment(formattedStartDate);

  // create an array to store the dates
  const datesList: string[] = [];

  // loop to generate dates for 7 days
  for (let day = 0; day < 7; day++) {
    datesList?.push(startDate?.format(dateTimeFormats?.DATE));

    // increment the date by 1 day
    startDate?.add(1, "days");
  }

  return datesList;
};

// function to set user notification toast
export const userNotification = (message: string, type?: string): void => {
  dispatch(
    openSnackbar({ open: true, message, alert: { color: type || "error" } })
  );
};

// function to get & validate the cookies
export const getCookies = (): UserSavedData | null => {
  const userDetails: string | undefined = Cookies?.get("tdUDetails");

  // verify if cookies exists
  if (userDetails) {
    try {
      return JSON.parse(userDetails);
    } catch (error) {
      return null;
    }
  }

  return null;
};

// function to updates cookies
export const updateCookies = (details: GenericMap): void => {
  // function call to get saved cookies
  const userDetails: UserSavedData | {} = getCookies() || {};

  // set user details to cookies
  Cookies?.set(
    "tdUDetails",
    JSON.stringify({ ...userDetails, ...details }),
    cookieOptions
  );
};

// function to validate country via currency
export const validateCountry = (currency: string): boolean => {
  return currency === pkCurrencyCode;
};

// function to return checkout customized settings
export const getCheckoutSettings = (
  checkoutSettings: BusinessCheckoutSettings | undefined
): CheckoutSettings => {
  return {
    submitFormButtonText: checkoutSettings?.button_text || "Place Order",
    forceUserAuth: checkoutSettings?.force_user_auth || false,
  };
};

// function to return modified payment methods
export const getPaymentMethods = (
  paymentOptions: PaymentOptions[],
  currency: string
): PaymentMethodType[] => {
  return (
    paymentOptions
      ?.filter((paymentOption) => paymentOption.enabled)
      ?.map((paymentOption) => ({
        label:
          paymentOption?.method === paymentTypes.COD.label
            ? validateCountry(currency)
              ? "Cash"
              : "Pay"
            : paymentOption?.name,
        value:
          paymentOption?.method === paymentTypes.COD.label
            ? paymentTypes.COD.value
            : paymentOption.method === paymentTypes.BANK_TRANSFER.label
            ? paymentTypes.BANK_TRANSFER.value
            : paymentTypes.CUSTOM.value,
        type: paymentOption?.method,
        accountId: paymentOption?.account_id,
        accountDetails: paymentOption?.account_details || null,
      })) || []
  );
};

// function to return minimum spend for an order
export const getMinSpend = (settings: string): number => {
  // parse the settings JSON
  const parsedSettings: BranchCartSettings = JSON.parse(settings);

  // return minimum spend if exists
  if (parsedSettings?.cart?.minimum_spent) {
    return Number(parsedSettings?.cart?.minimum_spent);
  }

  return 0;
};

// function to return branch available order types list
export const getBranchOrderTypes = (
  orderType: string,
  delivery: number,
  pickup: number
): string[] => {
  let orderTypeList: string[] = [];

  // verify order type not selected
  if (!orderType) {
    // push order type delivery if available
    if (delivery) {
      orderTypeList.push(orderTypes?.DELIVERY);
    }

    // push order type pickup if available
    if (pickup) {
      orderTypeList.push(orderTypes?.PICK_UP);
    }
  }

  return orderTypeList;
};

// function to set flag in store to enable/disable components
export const setDisableComponentFlag = (flag: boolean): void => {
  // set flag in store
  dispatch(updateCheckoutStore({ type: "disableComponentFlag", value: flag }));
};

// function to push GA4 events
export const pushGA4Events = (
  event: string,
  currency: string,
  cart: CartDetailsType,
  orderId: number,
  coupon = ""
): void => {
  // create event data object
  const eventData: GenericMap = {
    event,
    transaction_id: String(orderId),
    tax: Number(cart?.tax_value) || 0,
    shipping: Number(cart?.delivery_charges) || 0,
    coupon: coupon || "",
    currency,
    value: Number(cart?.gtotal) || 0,
    items:
      cart?.items?.map((item) => ({
        item_id: String(item?.menu_item_id) || "",
        item_name: String(item?.dname) || "",
        item_category: item?.category_name || "",
        price: Number(item?.dprice) || 0,
        quantity: Number(item?.dqty) || 1,
        discount: Number(item?.discount) || 0,
        coupon: coupon || "",
      })) || [],
  };

  // push to dataLayer for GA4 if initialized
  if (Array.isArray(window?.dataLayer)) {
    window?.dataLayer?.push(eventData);
  }

  // log GA4 data to firebase for debugging
  logGA4Data(eventData);
};

// function to push Facebook Pixel events
export const pushPixelEvents = (
  event: string,
  currency: string,
  cart: CartDetailsType,
  orderId: number,
  coupon = ""
): void => {
  // check if Facebook Pixel is initialized
  if (typeof window?.fbq !== "function") {
    return;
  }

  // create event data object for pixel tracking
  const eventData: GenericMap = {
    order_id: String(orderId),
    coupon: coupon || "",
    currency,
    value: Number(cart?.gtotal) || 0,
    content_type: "product",
    num_items: cart?.items?.length || 0,
    contentIds: cart?.items?.map((item) => String(item?.menu_item_id)) || [],
    contents:
      cart?.items?.map((item) => ({
        id: String(item.menu_item_id) || "",
        quantity: Number(item?.dqty) || 1,
        item_price: Number(item?.dprice) || 0,
      })) || [],
  };

  try {
    window?.fbq("track", event, eventData);
  } catch (error) {}
};

// function to validate phone number
export const validatePhoneNumber = (phone: string): boolean => {
  const phoneNumber = parsePhoneNumberFromString(phone);

  return phoneNumber?.isValid() || false;
};

// function to validate email address (returns true on failure to enable error flag)
export const validateEmail = (email: string): boolean => {
  return !emailValidationPattern.test(email?.trim());
};

// function to verify social login platform
export const verifySocialPlatform = (socialPlatform: string): boolean => {
  return socialPlatform === socialPlatforms?.GOOGLE;
};

// function to log exceptions to Sentry
export const captureErrorWithSentry = (error: unknown): void => {
  Sentry?.captureException(error);
};

// function to calculate luminance (intensity of color based on surface area)
export const calculateLuminance = (color: string): number => {
  let red: number = 0;
  let green: number = 0;
  let blue: number = 0;

  // if => color is in HEX format else if => color is in RGB or RGBA format
  if (color.startsWith("#")) {
    const hex: string = color.replace("#", "");

    // verify color length and convert to RGB
    if (hex.length === 3) {
      red = parseInt(hex[0] + hex[0], 16);
      green = parseInt(hex[1] + hex[1], 16);
      blue = parseInt(hex[2] + hex[2], 16);
    } else if (hex.length === 6) {
      red = parseInt(hex.slice(0, 2), 16);
      green = parseInt(hex.slice(2, 4), 16);
      blue = parseInt(hex.slice(4, 6), 16);
    } else {
      // invalid HEX color => return 1 to set default colors
      return 1;
    }
  } else if (color.startsWith("rgb")) {
    // verify color format and extract the color values
    const colors = color.match(
      /rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/
    );

    // verify color values and convert to RGB
    if (colors) {
      red = parseInt(colors[1], 10);
      green = parseInt(colors[2], 10);
      blue = parseInt(colors[3], 10);
    } else {
      // invalid RGB or RGBA color => return 1 to set default colors
      return 1;
    }
  } else {
    // invalid color => return 1 to set default colors
    return 1;
  }

  // normalize RGB values to the range [0, 1]
  const normalizedColors = [red, green, blue].map((color: number) =>
    color / 255 <= 0.03928
      ? color / 255 / 12.92
      : Math.pow((color / 255 + 0.055) / 1.055, 2.4)
  );

  // calculate luminance
  return (
    0.2126 * normalizedColors[0] +
    0.7152 * normalizedColors[1] +
    0.0722 * normalizedColors[2]
  );
};

// function to get the skeleton colors based on the theme
export const getSkeletonColors = (
  color: string | undefined
): SxProps<Theme> => {
  // default colors (for light theme)
  let backgroundColor = "rgba(0, 0, 0, 0.11)";
  let gradientBackgroundColor = "rgba(0, 0, 0, 0.04)";

  // function call to determine if a theme is dark
  if (color && calculateLuminance(color) < 0.5) {
    // update colors for dark theme
    backgroundColor = "rgba(238, 238, 238, 0.11)";
    gradientBackgroundColor = "rgba(238, 238, 238, 0.04)";
  }

  // return styles
  return {
    bgcolor: backgroundColor,

    "&.MuiSkeleton-root:after": {
      background: () =>
        `linear-gradient(90deg,transparent,${gradientBackgroundColor},transparent)`,
    },
  };
};

// function to return preauth value for create order
export const getPreAuthValue = (paymentType: PaymentMethodType): number => {
  /**
   * verify preauth is enabled for payment method
   * locating preauth key in accountDetails to prevent specific type assertion
   */
  if (
    paymentType?.accountDetails &&
    "preauth" in paymentType?.accountDetails &&
    paymentType?.accountDetails?.preauth
  ) {
    return 1;
  }

  return 0;
};
