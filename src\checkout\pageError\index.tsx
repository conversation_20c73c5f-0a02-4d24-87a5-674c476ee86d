import { Paper, Typography } from "@mui/material";

import Button from "components/button";
import {
  errorCode,
  errorMessage,
  errorPagePaper,
  homeButtonStyles,
  notFoundMessage,
} from "styles";
import { handleBackNavigation } from "utils/helperFunctions";
import { useSelector } from "store";

const PageError = () => {
  const { source, siteUrl, checkoutProcessState } = useSelector(
    (state) => state.checkout
  );

  // ==============================|| UI ||============================== //

  return (
    <Paper sx={errorPagePaper}>
      <Typography sx={errorCode}>{checkoutProcessState?.errorCode}</Typography>

      <Typography sx={notFoundMessage}>
        {checkoutProcessState?.statusMessage}
      </Typography>

      <Typography sx={errorMessage}>{checkoutProcessState?.message}</Typography>

      <Button
        sx={homeButtonStyles}
        onClick={() => handleBackNavigation(source, siteUrl)}
      >
        Back to home
      </Button>
    </Paper>
  );
};

export default PageError;
