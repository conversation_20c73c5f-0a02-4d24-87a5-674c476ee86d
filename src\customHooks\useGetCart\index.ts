import { useCallback, useEffect } from "react";

import useAxios from "axios-hooks";

import { CartDetailsType } from "types";
import { getCart } from "utils/apiConfig";
import {
  apiFailureMessage,
  setDisableComponentFlag,
} from "utils/helperFunctions";
import { dispatch, useSelector } from "store";
import { updateCheckoutStore } from "store/slices/checkout";

export const useGetCart = () => {
  const checkoutDetails = useSelector((state) => state.checkout);

  // ==============================|| API ||============================== //

  // API call to get cart details
  const [{ data, loading, error }, getCartAPICall] = useAxios(
    getCart(checkoutDetails?.businessId, checkoutDetails?.cartId),
    { manual: true }
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function to set the API loading state in store to disable components
    setDisableComponentFlag(loading);
  }, [loading]);

  // ==============================|| handler functions ||============================== //

  // function to call get cart API
  const callGetCartAPI =
    useCallback(async (): Promise<CartDetailsType | void> => {
      try {
        // API call to get cart details
        const { data } = await getCartAPICall();

        // verify API response
        if (data?.status === 200 && data?.result?.items?.length) {
          // set cart details in store
          dispatch(
            updateCheckoutStore({ type: "cartDetails", value: data.result })
          );

          return data.result;
        } else {
          // set message for user on api failure
          apiFailureMessage();
        }
      } catch (error) {
        // set message for user on api failure
        apiFailureMessage();
      }
    }, [getCartAPICall]);

  return {
    getCartData: data,
    getCartLoading: loading,
    error,
    getCartAPICall: callGetCartAPI,
  };
};
