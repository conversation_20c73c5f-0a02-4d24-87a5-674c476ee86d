import { useEffect, useRef, useState } from "react";

import {
  Typo<PERSON>,
  Stack,
  FormControlLabel,
  Radio,
  RadioGroup,
  FormControl,
  Card,
  Box,
  IconButton,
  CircularProgress,
} from "@mui/material";
import {
  Close,
  NavigateBeforeSharp,
  NavigateNextSharp,
  EventBusyTwoTone,
} from "@mui/icons-material";

import { useGetPickupHours } from "customHooks/useGetPickupHours";

import Modal from "components/modal";
import Button from "components/button";

import {
  cardRightScrollStyle,
  cardScrollStyle,
  cardsTabStyle,
  labelStyles,
  modalContentStack,
  modalFooterBox,
  modalHeaderBox,
  modalHeadingStyle,
  modalParentStack,
  noScheduleIcon,
  subscriptionCardBox,
  subscriptionCards,
  subscriptionCardStyle,
  tabContentTypoStyle,
  timeSlotStyles,
} from "checkout/checkoutForm/index.styles";
import { pointer } from "styles";
import { ScrollDirection } from "types";
import {
  SubscriptionDetails,
  SubscriptionModalProps,
  SubscriptionType,
} from "checkout/index.types";
import {
  borderColor,
  orderTypes,
  scrollDirections,
  subscriptionTypes,
} from "utils/constant";
import {
  generateSubscriptionDate,
  generateSubscriptionStructure,
  validateSubscription,
} from "utils/helperFunctions";
import { dispatch, useSelector } from "store";
import { bulkUpdateCheckoutStore } from "store/slices/checkout";

const SubscriptionSchedule: React.FC<SubscriptionModalProps> = ({
  selectedScheduleKey,
  toggleSubscriptionSchedule,
  handleToggleSubscriptionSchedule,
}) => {
  const checkoutDetails = useSelector((state) => state.checkout);

  // creating local state to avoid data reflection on main page
  const [subscriptionSchedule, setSubscriptionSchedule] =
    useState<SubscriptionType>(checkoutDetails?.subscription);

  const [timeSlotsList, setTimeSlotsList] = useState<string[]>([]);

  const [displayLeftArrow, setDisplayLeftArrow] = useState<boolean>(false);
  const [displayRightArrow, setDisplayRightArrow] = useState<boolean>(true);

  // using ref to set scroll in subscription day/date list
  const scrollRef = useRef<HTMLDivElement | null>(null);

  // ==============================|| custom hooks ||============================== //

  // custom hook to call get time slots API
  const { loading: getPickupHoursLoading, getPickupHoursAPICall } =
    useGetPickupHours();

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // set scroll event on subscription day/date list
    if (scrollRef.current) {
      // add scroll event listener to day/date cards list and toggle scroll icons
      scrollRef.current.addEventListener("scroll", toggleScrollIcons);

      // function to scroll to the initially selected subscription day/date
      scrollToSelectedDay();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [scrollRef.current]);

  useEffect(() => {
    // function to generate date from subscription day/date to call pickup hours API
    const subscriptionDate = generateSubscriptionDate(subscriptionSchedule);

    if (subscriptionDate) {
      // function to call get pickup hours API
      callGetPickupHoursAPI(subscriptionDate);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subscriptionSchedule?.details]);

  // ==============================|| handler functions ||============================== //

  // function to call get pickup hours API
  const callGetPickupHoursAPI = async (date: string): Promise<void> => {
    // get pickup hours API call
    const result: string[] = await getPickupHoursAPICall({
      params: {
        bid: checkoutDetails?.branchId,
        date,
        type: orderTypes?.PICK_UP,
      },
    });

    // set time slots list state
    setTimeSlotsList(result);

    // set the initial time slot if not exists
    if (!subscriptionSchedule?.time) {
      setSubscriptionSchedule({
        ...subscriptionSchedule,
        time: result?.[0] || "",
      });
    }
  };

  // function to handle the day/date selection for subscription
  const handleDaySelectionChange = (subscriptionKey: string): void => {
    // prevent card selection if API call in progress
    if (getPickupHoursLoading) {
      return;
    }

    // store selected subscription details for monthly subscription (multi-selection)
    let subscriptionData: SubscriptionDetails = subscriptionSchedule?.details;

    // store initial structure for single-selection in weekly/bi-weekly subscription
    if (
      subscriptionSchedule?.type === subscriptionTypes?.WEEKLY ||
      subscriptionSchedule?.type === subscriptionTypes?.BI_WEEKLY
    ) {
      // function to generate JSON for respective subscription type
      subscriptionData = generateSubscriptionStructure(
        subscriptionSchedule?.type
      );
    }

    // update state
    setSubscriptionSchedule({
      ...subscriptionSchedule,
      time: "",
      details: {
        ...subscriptionData,

        [subscriptionKey]: {
          ...subscriptionSchedule?.details[subscriptionKey],
          selected: !subscriptionSchedule?.details[subscriptionKey]?.selected,
        },
      },
    });
  };

  // function to handle the time selection
  const handleTimeSlotChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    // update state
    setSubscriptionSchedule({
      ...subscriptionSchedule,
      time: event?.target?.value || "",
    });
  };

  // function to handle the scroll transition in day/date cards
  const handleCardScroll = (direction: ScrollDirection): void => {
    if (scrollRef.current) {
      const scrollAmount: number = scrollRef.current.clientWidth;

      scrollRef.current.scrollBy({
        left:
          direction === scrollDirections?.LEFT ? -scrollAmount : scrollAmount,
        behavior: "smooth",
      });
    }
  };

  // function to toggle scroll icons based on container width
  const toggleScrollIcons = (): void => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current;

      // handle display of arrow on reaching the max-width
      setDisplayLeftArrow(scrollLeft > 0);

      // calculate width to scroll over the cards
      const maxScrollLeft: number = scrollWidth - clientWidth;

      // handle display of arrow on reaching the max-width
      setDisplayRightArrow(scrollLeft < maxScrollLeft - 1);
    }
  };

  // function to scroll to the initially selected subscription day/date
  const scrollToSelectedDay = () => {
    if (scrollRef.current) {
      // get the card element
      const cardElement =
        scrollRef.current.querySelectorAll(".card")[
          Number(selectedScheduleKey)
        ];

      if (cardElement) {
        // get card dimensions and its position relative to the container
        const cardRect = cardElement.getBoundingClientRect();

        // get scrolling box dimensions
        const boxRect = scrollRef.current.getBoundingClientRect();

        // calculate the offset to center the card
        const cardOffset = cardRect.left - boxRect.left;
        const centerOffset = (boxRect.width - cardRect.width) / 2;

        // scroll to center the selected card
        scrollRef.current.scrollTo({
          left: scrollRef.current.scrollLeft + cardOffset - centerOffset - 65,
          behavior: "smooth",
        });
      }
    }
  };

  // function to save data in store & close the modal
  const handleSaveLocation = async (): Promise<void> => {
    // update subscription schedule & remove validation error message (may exist) in store
    dispatch(
      bulkUpdateCheckoutStore({
        type: "",
        value: {
          subscription: subscriptionSchedule,
          validationError: {
            ...checkoutDetails?.validationError,
            subscription: false,
          },
        },
      })
    );

    // close the model
    handleToggleSubscriptionSchedule();
  };

  // ==============================|| UI ||============================== //

  // function to return the time slots
  const getScheduleHours = (): JSX.Element => {
    return timeSlotsList?.length ? (
      <FormControl>
        <RadioGroup
          aria-label="dynamic-radio-group"
          name="dynamic-radio-group"
          value={subscriptionSchedule?.time}
          onChange={handleTimeSlotChange}
          sx={timeSlotStyles}
        >
          {timeSlotsList?.map((slot) => (
            <FormControlLabel
              key={slot}
              value={slot}
              control={<Radio />}
              label={slot}
              labelPlacement="start"
            />
          ))}
        </RadioGroup>
      </FormControl>
    ) : (
      <>
        {!getPickupHoursLoading && (
          <Stack sx={{ alignItems: "center" }}>
            <EventBusyTwoTone sx={noScheduleIcon} />

            <Typography sx={labelStyles}>
              Oops! The slot is not available. Select another
            </Typography>
            <Typography sx={labelStyles}>day/date</Typography>
          </Stack>
        )}
      </>
    );
  };

  // function to return the subscription day/date cards labels
  const getScheduleDayLabel = (subscriptionKey: string): JSX.Element => {
    const dayName: string = subscriptionSchedule?.details[subscriptionKey]?.day;

    // add 's' to make day names plural for weekly/bi-weekly subscriptions
    const displayName: string =
      subscriptionSchedule?.type === subscriptionTypes?.WEEKLY ||
      subscriptionSchedule?.type === subscriptionTypes?.BI_WEEKLY
        ? `${dayName}s`
        : dayName;

    return (
      <>
        {/* display loader only on API call & selected schedule */}
        {getPickupHoursLoading &&
          subscriptionSchedule?.details[subscriptionKey]?.selected && (
            <CircularProgress title="Loader" size={24} sx={cardsTabStyle} />
          )}

        {/* get the day/date UI */}
        {displayName}
      </>
    );
  };

  return (
    <Modal
      open={toggleSubscriptionSchedule}
      onClose={handleToggleSubscriptionSchedule}
    >
      <Stack sx={modalParentStack}>
        <Stack>
          <Box sx={modalHeaderBox}>
            <Typography sx={modalHeadingStyle}>
              Schedule subscription
            </Typography>

            <Close sx={pointer} onClick={handleToggleSubscriptionSchedule} />
          </Box>

          <Stack spacing={2.5} sx={modalContentStack}>
            <Box sx={subscriptionCardBox}>
              {displayLeftArrow && (
                <IconButton
                  onClick={() =>
                    handleCardScroll(scrollDirections?.LEFT as ScrollDirection)
                  }
                  sx={cardScrollStyle}
                >
                  <NavigateBeforeSharp />
                </IconButton>
              )}

              <Box ref={scrollRef} sx={subscriptionCards}>
                {Object.keys(subscriptionSchedule?.details)?.map((key) => {
                  return (
                    <Card
                      className="card"
                      key={key}
                      variant="outlined"
                      onClick={() => handleDaySelectionChange(key)}
                      sx={{
                        ...subscriptionCardStyle,

                        borderColor: subscriptionSchedule?.details[key]
                          ?.selected
                          ? "#000000"
                          : borderColor,
                        backgroundColor:
                          getPickupHoursLoading &&
                          subscriptionSchedule?.details[key]?.selected
                            ? "#0000001f"
                            : "#FFFFFF",
                        pointerEvents: checkoutDetails?.disableComponentFlag
                          ? "none"
                          : "unset",
                      }}
                    >
                      <Typography
                        sx={{
                          ...tabContentTypoStyle,

                          color: subscriptionSchedule?.details[key]?.selected
                            ? "#000000"
                            : "rgba(0, 0, 0, 0.50)",

                          fontWeight: subscriptionSchedule?.details[key]
                            ?.selected
                            ? 600
                            : 400,
                        }}
                      >
                        {getScheduleDayLabel(key)}
                      </Typography>
                    </Card>
                  );
                })}
              </Box>

              {displayRightArrow && (
                <IconButton
                  onClick={() =>
                    handleCardScroll(scrollDirections?.RIGHT as ScrollDirection)
                  }
                  sx={cardRightScrollStyle}
                >
                  <NavigateNextSharp />
                </IconButton>
              )}
            </Box>

            {/* schedule time slots UI */}
            {getScheduleHours()}
          </Stack>
        </Stack>

        <Box sx={modalFooterBox}>
          <Button
            onClick={handleSaveLocation}
            disabled={!validateSubscription(subscriptionSchedule)}
          >
            Save Schedule
          </Button>
        </Box>
      </Stack>
    </Modal>
  );
};

export default SubscriptionSchedule;
