import { ElementIds } from "checkout/index.types";

// ==============================|| initial values ||============================== //

export const initialTip = {
  type: 0,
  label: "",
  value: 0,
  pickup: false,
  enabled: false,
  delivery: false,
};

export const initialPaymentMethod = {
  label: "Cash",
  value: "0",
  type: "cod",
  accountId: "",
  accountDetails: null,
};

export const socialLoginDefaultStatus = {
  google: false,
  facebook: false,
};

export const initialOrderSchedule = { date: "", time: "" };

export const tipsList = ["5", "10", "15", "Custom"];

// google map custom configurations
export const googleMapOptions = {
  mapTypeControl: false, // disables the map/satellite control
  streetViewControl: false, // disables the street view control
  fullscreenControl: false, // disables the full screen view control
  gestureHandling: "greedy", // enables scroll with single finger
};

// checkout process confirmation/failure page messages
export const orderSuccess = "Order placed successfully";
export const subscriptionSuccess = "Subscription successful";
export const transactionSuccess = "Transaction completed successfully";
export const transactionFailure = "Transaction failed";

export const orderSuccessMessage =
  "Sit and relax while your order is being prepared";
export const subscriptionSuccessMessage =
  "Your subscription has been successfully activated";
export const transactionFailureMessage =
  "Your transaction has been failed due to an unknown error";
export const paymentMethodFailure =
  "Failed to load the payment method due to an unknown error";
export const pageNotFound = "Whoops! Page not found";
export const pageErrorMessage = `This page is out of service. But don't worry, our main site is all systems go`;
export const dataFailure = "Whoops! Something went wrong";
export const dataFailureMessage = `It looks like there’s been an error in loading the data. Please try refreshing the page or come back later.`;

// ==============================|| regular expressions ||============================== //

// regex to validate email address
export const emailValidationPattern =
  /^(?!.*\.\.)[a-zA-Z0-9](?:[a-zA-Z0-9._]*[a-zA-Z0-9])?@[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?(?:\.[a-zA-Z]{2,63})+$/;

// ==============================|| constants ||============================== //

export const mapZoomLevel = 18;
export const pkCurrencyCode = "PKR";
export const debouncePeriod = 600;
export const cookieOptions = { expires: 30, path: "/", sameSite: "lax" };
export const stripeMinSpend = 0.5;

// storing ids to perform scroll to the element
export const UIElementIds: ElementIds = {
  name: "userName",
  email: "userEmail",
  phone: "userPhone",
  deliveryAddress: "userAddress",
  orderSchedule: "orderSchedule",
  subscription: "subscriptionSchedule",
  coupon: "couponCode",
  bankTransfer: "bankTransfer",
};

// ==============================|| labels ||============================== //

/**
 * '0' => COD
 * '2' => bank transfer
 * '3' => other payment methods
 */
export const paymentTypes = {
  COD: { value: "0", label: "cod" },
  BANK_TRANSFER: { value: "2", label: "bank_transfer" },
  CUSTOM: { value: "3", label: "custom" },
};

// payment ids in database
export const paymentMethods = {
  STRIPE: { value: "stripe", id: 0 },
  BANK_ALFALAH: { value: "bank_alfalah", id: 110441 },
  PAY_MOB: { value: "paymob", id: 0 },
  PAY_FAST: { value: "payfast", id: 649559 },
  BANK_TRANSFER: { value: "bank_transfer", id: 0 },
};

export const scheduleTypes = { NOW: "now", LATER: "schedule" };

export const subscriptionTypes = {
  WEEKLY: "weekly",
  BI_WEEKLY: "bi-weekly",
  MONTHLY: "monthly",
};

export const subscriptionMessage = {
  WEEKLY: "Every Monday - 2:00 PM",
  BI_WEEKLY: "Every alternate Monday - 2:00 PM",
  MONTHLY: "Every month on the 1st - 2:00 PM",
};

export const scheduleType = { ORDER: "order", SUBSCRIPTION: "subscription" };

export const orderTypes = { PICK_UP: "pickup", DELIVERY: "delivery" };

export const deliverySettingOptions = {
  LOCALITY: "locality",
  GEO_RANGE: "geo_range",
};

export const dateTimeFormats = {
  DATE: "YYYY-MM-DD",
  TIME: "HH:mm:ss",
  TIME_MERIDIAN: "h:mm:ss A",
  MINUTES_TIME_MERIDIAN: "h:mm A",
  INITIAL_DATE_TIME: "0000-00-00 00:00:00",
  DATE_TIME: "YYYY-MM-DDTHH:mm:ss[Z]",
  DATE_TIME_MILLISECONDS: "YYYY-MM-DD HH:mm:ss:SSS",
  DAY_MONTH: "DD MMM",
  DAY: "dddd",
  MONTH_DATE: "MMM DD, YYYY",
};

export const userAuthTypes = {
  LOGIN: "Login",
  SIGN_UP: "Sign up",
  RESET_PASSWORD: "Reset Password",
};

export const scrollDirections = {
  LEFT: "left",
  RIGHT: "right",
};

export enum GA4Events {
  BEGIN_CHECKOUT = "begin_checkout",
  PURCHASE = "purchase",
  ADD_SHIPPING_INFO = "add_shipping_info",
  ADD_PAYMENT_INFO = "add_payment_info",
}

export enum PixelEvents {
  INITIATE_CHECKOUT = "InitiateCheckout",
  PURCHASE = "Purchase",
}

export const cardIconsPath: Record<string, string> = {
  VISA: "https://static.tossdown.com/site/323849fa-f3f8-47c1-b3d2-98d6f330f6d0.webp",
  MASTERCARD:
    "https://static.tossdown.com/site/5ad3ce91-afb7-4a63-9307-b8abbea02570.webp",
  GPAY: "https://static.tossdown.com/site/99c1cbfe-6546-4ac6-9bbd-60997259bddc.webp",
  APAY: "https://static.tossdown.com/site/e46642d9-6466-4cdc-9dba-0e8d28bb359b.webp",
};

export const cardPlaceholder = "xxxx xxxx xxxx";

export const sourceTypes = {
  WEB: "web",
  WEB_ORDRZ: "ordrz",
  WEB_OBW: "obw",
  APP: "app",
};

export const socialPlatforms = {
  GOOGLE: "google",
  FACEBOOK: "facebook",
};

export const firebaseCollections = { LOGS: "API_LOGS", GA4: "GA4" };

export const environments = {
  DEVELOPMENT: "development",
  PRODUCTION: "production",
};

export const phoneSignupError = "Invalid number";
export const emailError = "Invalid email address";
export const phoneErrorMessage = "Please enter a valid phone number";
export const emailErrorMessage = "Please enter a valid email address";

// ==============================|| UI constants ||============================== //

export const skeletonAnimation = "wave";
export const borderRadius = "6px";
export const borderColor = "#EBEBEB";
