import { Stack, Typography } from "@mui/material";

import {
  deliveryAddress,
  labelStyles,
} from "checkout/checkoutForm/index.styles";
import { concatStrings } from "utils/helperFunctions";
import { useSelector } from "store";

const PickupDetails = () => {
  const { branchDetails } = useSelector((state) => state.business);

  // ==============================|| UI ||============================== //

  // function to return the branch address
  const getBranchAddress = (): string => {
    return concatStrings([
      branchDetails?.address,
      branchDetails?.area,
      branchDetails?.city,
      branchDetails?.country,
    ]);
  };

  return (
    // render the pickup branch address
    <Stack>
      <Typography sx={labelStyles}>Pickup location*</Typography>

      <Typography sx={deliveryAddress}>{getBranchAddress()}</Typography>
    </Stack>
  );
};

export default PickupDetails;
