import { Box, Grid, Typography } from "@mui/material";
import {
  PhoneInput as InputField,
  defaultCountries,
  parseCountry,
} from "react-international-phone";
import "react-international-phone/style.css";

import Tooltip from "components/tooltip";
import { signupPhoneError } from "components/index.styles";
import { inputLabelStyle, relativePosition } from "styles";
import { PhoneInputProps } from "components/index.types";
import { UIElementIds } from "utils/constant";
import { useSelector } from "store";

const PhoneInput = ({
  label,
  value,
  onChange,
  labelStyles,
  error,
  helperText,
  toggleTooltip,
}: PhoneInputProps) => {
  const { branchDetails } = useSelector((state) => state.business);

  // ==============================|| handler functions ||============================== //

  // function to get the country code of branch country
  const getCountryCode = (): string => {
    let countryCode: string = "";

    // get the country code of branch country from pkg country list
    defaultCountries.forEach((country) => {
      const parsedCountry = parseCountry(country);

      if (parsedCountry.name === branchDetails.country) {
        countryCode = parsedCountry.iso2;
      }
    });

    return countryCode || "";
  };

  // ==============================|| UI ||============================== //

  return (
    <Grid item sx={relativePosition}>
      {/* display a label if passed */}
      {label && (
        <Typography
          id={UIElementIds?.phone}
          component="small"
          sx={{ ...inputLabelStyle, ...labelStyles }}
        >
          {label}
        </Typography>
      )}

      {/* set error in tooltip if required */}
      <Tooltip open={toggleTooltip || false} message={helperText}>
        <Box>
          <InputField
            defaultCountry={getCountryCode()}
            value={value}
            forceDialCode
            onChange={onChange}
          />
        </Box>
      </Tooltip>

      {/* display error message inside the field */}
      {error && <Typography sx={signupPhoneError}>{helperText}</Typography>}
    </Grid>
  );
};

export default PhoneInput;
