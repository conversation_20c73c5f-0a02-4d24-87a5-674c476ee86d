import { useState } from "react";

import {
  TextField as M<PERSON><PERSON>ex<PERSON><PERSON><PERSON>,
  InputAdornment,
  IconButton,
} from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";

import { inputStyles } from "styles";
import { fieldErrorMessage, viewPasswordIcon } from "components/index.styles";
import { TextFieldProps } from "components/index.types";

const TextField = ({
  id,
  name,
  placeholder,
  variant,
  label,
  value,
  autoFocus,
  onKeyPress,
  onChange,
  styles,
  type,
  inputProps,
  error,
  helperText,
  disable,
  textTransform,
}: TextFieldProps) => {
  const [showPassword, setShowPassword] = useState<boolean>(false);

  // ==============================|| handler functions ||============================== //

  // function to toggle the password visibility
  const handlePasswordVisibility = (): void => {
    setShowPassword((prevState) => !prevState);
  };

  // ==============================|| UI ||============================== //

  return (
    <MUITextField
      id={id}
      name={name}
      placeholder={placeholder}
      label={label}
      value={value}
      autoFocus={autoFocus}
      onKeyPress={onKeyPress}
      onChange={onChange}
      sx={{
        ...(styles || inputStyles),

        // hide the default password visibility icon
        "& .MuiInputBase-input::-ms-reveal": { display: "none" },
      }}
      type={type === "password" && showPassword ? "text" : type}
      disabled={disable}
      variant={variant || "standard"}
      error={error}
      inputProps={{
        style: { textTransform: textTransform || "none" },
      }}
      InputProps={{
        endAdornment: error ? (
          <InputAdornment position="end">
            <span style={fieldErrorMessage}>{helperText || "Required"}</span>
          </InputAdornment>
        ) : type === "password" && value ? (
          <InputAdornment position="end">
            <IconButton
              edge="end"
              onClick={handlePasswordVisibility}
              sx={viewPasswordIcon}
            >
              {showPassword ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </InputAdornment>
        ) : null,

        ...inputProps,
      }}
      InputLabelProps={{ shrink: !!value || undefined }}
    />
  );
};

export default TextField;
