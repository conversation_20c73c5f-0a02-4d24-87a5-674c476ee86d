import { useEffect, useState } from "react";

import { Grid, Stack, Typography } from "@mui/material";
import { Close } from "@mui/icons-material";
import useAxios from "axios-hooks";

import Modal from "components/modal";
import Button from "components/button";
import TextField from "components/textField";
import Autocomplete from "components/autocomplete";
import GoogleMaps from "./googleMaps";
import LeafletMaps from "./leafletMaps";

import {
  addressDetailsStyles,
  addressPreview,
  addressPreviewGrid,
  deliveryDetailsGrid,
  modalHeading,
  deliveryParentGrid,
  radiusErrorStyles,
} from "checkout/checkoutForm/index.styles";
import { outlinedInputStyle, pointer } from "styles";
import { DeliveryModalProps } from "checkout/index.types";
import { ErrorResponse } from "types";
import {
  getCartCalculationPayload,
  getCartCalculations,
  setCartCalculationResponse,
} from "utils/apiConfig";
import { deliverySettingOptions, GA4Events } from "utils/constant";
import {
  capitalizeFirstLetter,
  concatStrings,
  pushGA4Events,
  updateCookies,
} from "utils/helperFunctions";
import { dispatch, useSelector } from "store";
import {
  bulkUpdateCheckoutStore,
  updateCheckoutStore,
} from "store/slices/checkout";

const DeliveryDetailsMapModal: React.FC<DeliveryModalProps> = ({
  toggleDeliveryModal,
  handleToggleDeliveryModal,
}) => {
  const [radiusError, setRadiusError] = useState<string>("");

  const [cityList, setCityList] = useState<string[]>([]);
  const [areaList, setAreaList] = useState<string[]>([]);

  const { businessDetails, branchDetails } = useSelector(
    (state) => state.business
  );
  const checkoutDetails = useSelector((state) => state.checkout);

  // ==============================|| API ||============================== //

  // API call to get cart calculations
  const [{ loading: getCartCalculationsLoading }, getCartCalculationsAPICall] =
    useAxios(getCartCalculations(), { manual: true });

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    if (
      branchDetails?.deliverySettings?.charges_type ===
      deliverySettingOptions.LOCALITY
    ) {
      let primaryLocalityList: string[] = Array.from(
        new Set(
          branchDetails?.deliverySettings?.charges_details?.charges?.map(
            (charge: { primary_locality: string }) => charge.primary_locality
            // TODO: required after backend implementation
            // capitalizeFirstLetter(charge.primary_locality)
          )
        )
      );

      setCityList(primaryLocalityList);

      // set first city from list if no city exists
      if (primaryLocalityList?.length && !checkoutDetails?.userCity) {
        dispatch(
          bulkUpdateCheckoutStore({
            type: "",
            value: { userCity: primaryLocalityList[0], userArea: "" },
          })
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const parseAreas = (areasString: string): string[] => {
    return areasString?.split(",")?.map(
      (area) => area.trim()
      // TODO: required after backend implementation
      // capitalizeFirstLetter(area.trim())
    );
  };

  useEffect(() => {
    if (
      checkoutDetails?.userCity &&
      branchDetails?.deliverySettings?.charges_type ===
        deliverySettingOptions.LOCALITY
    ) {
      let selectedPrimaryLocality =
        branchDetails?.deliverySettings?.charges_details?.charges
          ?.filter(
            (charge: { primary_locality: string }) =>
              charge.primary_locality === checkoutDetails?.userCity
          )
          ?.flatMap((charge: { secondary_locality: string }) =>
            parseAreas(charge.secondary_locality)
          );

      setAreaList(selectedPrimaryLocality?.[0] ? selectedPrimaryLocality : []);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checkoutDetails?.userCity]);

  useEffect(() => {
    if (radiusError) {
      setRadiusError("");
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checkoutDetails?.userLocation]);

  // ==============================|| handler functions ||============================== //

  // function to handle the fields input change
  const handleInputChange = (event: {
    target: { name: string; value: string };
  }) => {
    // set input data in store
    dispatch(
      updateCheckoutStore({
        type: event.target.name,
        value: event.target.value,
      })
    );

    // reset validation error
    if (checkoutDetails?.validationError[event.target.name]) {
      dispatch(
        updateCheckoutStore({
          type: "validationError",
          value: {
            ...checkoutDetails?.validationError,
            [event.target.name]: false,
          },
        })
      );
    }
  };

  // function to handle the cart calculation API & save details
  const handleSaveLocation = async () => {
    // sets the key true if condition fails
    let dataValidation = {
      streetAddress: !checkoutDetails?.streetAddress?.trim(),
      userArea: !checkoutDetails?.userArea?.trim(),
      userCity: !checkoutDetails?.userCity?.trim(),
    };

    // checks if any of the error is true
    if (Object.values(dataValidation)?.some((value) => value)) {
      // set data in store to reflect errors
      dispatch(
        updateCheckoutStore({
          type: "validationError",
          value: { ...checkoutDetails?.validationError, ...dataValidation },
        })
      );

      return;
    }

    try {
      const { data: calculatedCartData } = await getCartCalculationsAPICall(
        getCartCalculationPayload(
          checkoutDetails,
          checkoutDetails?.paymentType.value,
          checkoutDetails.orderType,
          checkoutDetails.couponCode,
          checkoutDetails.cartDetails.tip
        )
      );

      if (calculatedCartData?.status === 200 && calculatedCartData.result) {
        setCartCalculationResponse(
          checkoutDetails.cartDetails,
          calculatedCartData?.result
        );

        // set calculated cart to store & reset user location error
        dispatch(
          bulkUpdateCheckoutStore({
            type: "",
            value: {
              address: concatStrings([
                capitalizeFirstLetter(checkoutDetails?.streetAddress),
                capitalizeFirstLetter(checkoutDetails?.userArea),
                capitalizeFirstLetter(checkoutDetails?.userCity),
              ]),
              userLocationFlag: false,
              validationError: {
                ...checkoutDetails?.validationError,
                deliveryAddress: false,
              },
            },
          })
        );

        // reset local error state
        setRadiusError("");

        // set user address in cookies
        updateCookies({
          address: concatStrings([
            capitalizeFirstLetter(checkoutDetails?.streetAddress),
            capitalizeFirstLetter(checkoutDetails?.userArea),
            capitalizeFirstLetter(checkoutDetails?.userCity),
          ]),

          streetAddress: checkoutDetails?.streetAddress,
          userArea: capitalizeFirstLetter(checkoutDetails?.userArea),
          userCity: capitalizeFirstLetter(checkoutDetails?.userCity),
          userPostalCode: checkoutDetails?.userPostalCode,
          userState: checkoutDetails?.userState,
          userCountry: checkoutDetails?.userCountry,
          userLocation: checkoutDetails?.userLocation,
        });

        // GA4 | function call to log GA4 events (event => add shipping info)
        pushGA4Events(
          GA4Events?.ADD_SHIPPING_INFO,
          businessDetails?.currencyCode,
          checkoutDetails?.cartDetails,
          0
        );

        // close delivery modal
        handleToggleDeliveryModal();
      }
    } catch (error) {
      setRadiusError((error as ErrorResponse).message || "");

      // set location flag to true to prevent post order
      dispatch(updateCheckoutStore({ type: "userLocationFlag", value: true }));
    }
  };

  // ==============================|| UI ||============================== //

  const getAreaField = () => {
    return (
      <Grid item>
        <TextField
          name="userArea"
          variant="outlined"
          label="Area"
          value={checkoutDetails?.userArea}
          textTransform={"capitalize"}
          onChange={handleInputChange}
          styles={outlinedInputStyle}
          error={checkoutDetails?.validationError.userArea}
        />
      </Grid>
    );
  };

  const getCityField = () => {
    return (
      <Grid item>
        <TextField
          name="userCity"
          variant="outlined"
          label="City"
          value={checkoutDetails?.userCity}
          textTransform={"capitalize"}
          onChange={handleInputChange}
          styles={{
            ...outlinedInputStyle,
            mt: { xs: "10px", sm: "0px" },
          }}
          error={checkoutDetails?.validationError.userCity}
        />
      </Grid>
    );
  };

  const getCityDropdown = () => {
    return (
      <Grid item>
        <Autocomplete
          label="City"
          value={checkoutDetails?.userCity}
          options={cityList}
          onChange={(event, newValue) => {
            if (!newValue) {
              return;
            }

            // set input data in store
            dispatch(
              bulkUpdateCheckoutStore({
                type: "",
                value: { userCity: newValue, userArea: "" },
              })
            );

            // reset validation error
            if (checkoutDetails?.validationError["userCity"]) {
              dispatch(
                updateCheckoutStore({
                  type: "validationError",
                  value: {
                    ...checkoutDetails?.validationError,
                    userCity: false,
                  },
                })
              );
            }

            // reset error message if any
            if (radiusError) {
              setRadiusError("");
            }
          }}
          error={checkoutDetails?.validationError.userCity}
        />
      </Grid>
    );
  };

  const getAreaDropdown = () => {
    return (
      <Grid item>
        <Autocomplete
          label="Area"
          value={checkoutDetails?.userArea}
          options={areaList}
          onChange={(event, newValue, name) => {
            if (!newValue) {
              return;
            }

            // set input data in store
            dispatch(
              updateCheckoutStore({
                type: "userArea",
                value: newValue,
              })
            );

            // reset validation error
            if (checkoutDetails?.validationError["userArea"]) {
              dispatch(
                updateCheckoutStore({
                  type: "validationError",
                  value: {
                    ...checkoutDetails?.validationError,
                    userArea: false,
                  },
                })
              );
            }

            // reset error message if any
            if (radiusError) {
              setRadiusError("");
            }
          }}
          error={checkoutDetails?.validationError.userArea}
        />
      </Grid>
    );
  };

  const getCityAreaUI = () => {
    return (
      <>
        {getCityDropdown()}

        {areaList?.length ? getAreaDropdown() : getAreaField()}
      </>
    );
  };

  return (
    <Modal open={toggleDeliveryModal}>
      <Grid container sx={deliveryParentGrid}>
        <Grid
          item
          sx={{
            position: "sticky",
            zIndex: 99999,
            bottom: "0px",
            background: "#fff",
            padding: "10px 20px 0px 20px",
            top: "0px",
            boxShadow: "0px 4px 4px 0px rgba(0, 0, 0, 0.05)",
            mb: "20px",
          }}
        >
          <Typography component="strong" sx={modalHeading}>
            Address
            <Close sx={pointer} onClick={handleToggleDeliveryModal} />
          </Typography>

          {concatStrings([
            checkoutDetails?.streetAddress,
            checkoutDetails?.userArea,
            checkoutDetails?.userCity,
          ]) && (
            <Grid item sx={addressPreviewGrid}>
              <Typography
                sx={{
                  ...addressPreview,

                  fontWeight: concatStrings([
                    checkoutDetails?.streetAddress,
                    checkoutDetails?.userArea,
                    checkoutDetails?.userCity,
                  ])
                    ? 700
                    : 400,
                  color: concatStrings([
                    // apartment,
                    checkoutDetails?.streetAddress,
                    checkoutDetails?.userArea,
                    checkoutDetails?.userCity,
                  ])
                    ? "#000000"
                    : "#BABABA",
                }}
              >
                {concatStrings([
                  capitalizeFirstLetter(checkoutDetails?.streetAddress),
                  capitalizeFirstLetter(checkoutDetails?.userArea),
                  capitalizeFirstLetter(checkoutDetails?.userCity),
                ])}
              </Typography>
            </Grid>
          )}
        </Grid>

        <Grid item sx={deliveryDetailsGrid}>
          <Grid item>
            <TextField
              name="streetAddress"
              variant="outlined"
              label="Street address"
              textTransform={"capitalize"}
              value={checkoutDetails?.streetAddress}
              error={checkoutDetails?.validationError.streetAddress}
              onChange={handleInputChange}
              styles={{ ...outlinedInputStyle, m: "10px 0" }}
            />
          </Grid>

          <Grid container sx={addressDetailsStyles}>
            {branchDetails?.deliverySettings?.charges_type !==
            deliverySettingOptions.LOCALITY ? (
              <>
                {getAreaField()}

                {getCityField()}
              </>
            ) : (
              <>{getCityAreaUI()}</>
            )}
          </Grid>

          {branchDetails?.deliverySettings?.charges_type !==
            deliverySettingOptions.LOCALITY &&
            (businessDetails.googleKey ? <GoogleMaps /> : <LeafletMaps />)}
        </Grid>

        <Stack
          sx={{
            position: "sticky",
            zIndex: "99999",
            bottom: "0px",
            background: "#fff",
            padding: "15px 20px 15px 20px",
            marginTop: "15px",
            borderTop: "1px solid rgba(0, 0, 0, 0.12)",
          }}
        >
          {radiusError && (
            <Typography
              sx={{ ...radiusErrorStyles, position: "unset", pb: "10px" }}
            >
              {radiusError}
            </Typography>
          )}

          <Button
            loading={getCartCalculationsLoading}
            disabled={getCartCalculationsLoading}
            onClick={handleSaveLocation}
          >
            Add Address
          </Button>
        </Stack>
      </Grid>
    </Modal>
  );
};

export default DeliveryDetailsMapModal;
