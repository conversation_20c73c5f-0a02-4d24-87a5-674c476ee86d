import { useEffect, useState } from "react";

import { Grid, Typography } from "@mui/material";
import { Close } from "@mui/icons-material";
import useAxios from "axios-hooks";

import Modal from "components/modal";
import Button from "components/button";
import TextField from "components/textField";
import LeafletProvider from "./leafletProvider";
import GoogleProvider from "./googleProvider";

import {
  addressDetailsStyles,
  addressPreview,
  addressPreviewGrid,
  deliveryDetailsGrid,
  modalHeading,
} from "checkout/checkoutForm/index.styles";
import { errorMessageStyles, outlinedInputStyle, pointer } from "styles";
import { DeliveryModalProps } from "checkout/index.types";
import { GA4Events } from "utils/constant";
import {
  getCartCalculations,
  getCartCalculationPayload,
  setCartCalculationResponse,
} from "utils/apiConfig";
import {
  capitalizeFirstLetter,
  concatStrings,
  pushGA4Events,
  updateCookies,
} from "utils/helperFunctions";

import { dispatch, useSelector } from "store";
import {
  bulkUpdateCheckoutStore,
  updateCheckoutStore,
} from "store/slices/checkout";

const DeliveryDetailsModal: React.FC<DeliveryModalProps> = ({
  toggleDeliveryModal,
  handleToggleDeliveryModal,
}) => {
  const [radiusError, setRadiusError] = useState<string>("");

  const checkoutDetails = useSelector((state) => state.checkout);
  const { businessDetails } = useSelector((state) => state.business);

  // ==============================|| API ||============================== //

  // API call to get cart calculations
  const [{ loading: getCartCalculationsLoading }, getCartCalculationsAPICall] =
    useAxios(getCartCalculations(), { manual: true });

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    if (radiusError) {
      setRadiusError("");
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [checkoutDetails?.userLocation]);

  // ==============================|| handler functions ||============================== //

  // function to handle the fields input change
  const handleInputChange = (event: {
    target: { name: string; value: string };
  }) => {
    // set data in store
    dispatch(
      updateCheckoutStore({
        type: event.target.name,
        value: event.target.value,
      })
    );

    if (event.target.name === "userPostalCode") {
      dispatch(
        updateCheckoutStore({
          type: "userArea",
          value: " ",
        })
      );
    }

    // reset validation error
    if (checkoutDetails?.validationError[event.target.name]) {
      dispatch(
        updateCheckoutStore({
          type: "validationError",
          value: {
            ...checkoutDetails?.validationError,
            [event.target.name]: false,
          },
        })
      );
    }
  };

  // function to handle the cart calculation API
  const handleSaveLocation = async () => {
    // sets the key true if condition fails
    let dataValidation = {
      userCity: !checkoutDetails?.userCity?.trim(),
      userPostalCode: !checkoutDetails?.userPostalCode?.trim(),
    };

    // checks if any of the error is true
    if (Object.values(dataValidation)?.some((value) => value)) {
      // set data in store to reflect errors
      dispatch(
        updateCheckoutStore({
          type: "validationError",
          value: { ...checkoutDetails?.validationError, ...dataValidation },
        })
      );

      return;
    }

    try {
      const { data: calculatedCartData } = await getCartCalculationsAPICall(
        getCartCalculationPayload(
          checkoutDetails,
          checkoutDetails?.paymentType.value,
          checkoutDetails.orderType,
          checkoutDetails.couponCode,
          checkoutDetails.cartDetails.tip
        )
      );

      if (calculatedCartData?.status === 200 && calculatedCartData.result) {
        setCartCalculationResponse(
          checkoutDetails.cartDetails,
          calculatedCartData?.result
        );

        // set calculated cart to store & reset user location error
        dispatch(
          bulkUpdateCheckoutStore({
            type: "",
            value: {
              address: concatStrings([
                capitalizeFirstLetter(checkoutDetails?.apartment),
                capitalizeFirstLetter(checkoutDetails?.streetAddress),
                capitalizeFirstLetter(checkoutDetails?.userCity),
                checkoutDetails?.userPostalCode,

                checkoutDetails?.userCountry,
              ]),
              userLocationFlag: false,
              validationError: {
                ...checkoutDetails?.validationError,
                deliveryAddress: false,
              },
            },
          })
        );

        // reset local error state
        setRadiusError("");

        // set user address in cookies
        updateCookies({
          address: concatStrings([
            capitalizeFirstLetter(checkoutDetails?.apartment),
            capitalizeFirstLetter(checkoutDetails?.streetAddress),
            capitalizeFirstLetter(checkoutDetails?.userCity),
            checkoutDetails?.userPostalCode,

            checkoutDetails?.userCountry,
          ]),
          apartment: capitalizeFirstLetter(checkoutDetails?.apartment),
          userArea: capitalizeFirstLetter(checkoutDetails?.userArea),
          userCity: capitalizeFirstLetter(checkoutDetails?.userCity),

          userPostalCode: checkoutDetails?.userPostalCode,
          userState: checkoutDetails?.userState,
          userCountry: checkoutDetails?.userCountry,

          userLocation: checkoutDetails?.userLocation,
        });

        // GA4 | function call to log GA4 events (event => add shipping info)
        pushGA4Events(
          GA4Events?.ADD_SHIPPING_INFO,
          businessDetails?.currencyCode,
          checkoutDetails?.cartDetails,
          0
        );

        // close delivery modal
        handleToggleDeliveryModal();
      }
    } catch (error) {
      setRadiusError("Selected location is out of our delivery range!");

      // set location flag to true to prevent post order
      dispatch(updateCheckoutStore({ type: "userLocationFlag", value: true }));
    }
  };

  // ==============================|| UI ||============================== //

  return (
    <Modal open={toggleDeliveryModal}>
      <Grid item sx={{ ...deliveryDetailsGrid, p: "20px" }}>
        <Typography component="strong" sx={modalHeading}>
          Address
          <Close sx={pointer} onClick={handleToggleDeliveryModal} />
        </Typography>

        {concatStrings([
          checkoutDetails?.apartment,
          checkoutDetails?.userArea,
          checkoutDetails?.userCity,
        ]) && (
          <Grid item sx={addressPreviewGrid}>
            <Typography
              sx={{
                ...addressPreview,

                fontWeight: concatStrings([
                  checkoutDetails?.apartment,
                  checkoutDetails?.userArea,
                  checkoutDetails?.userCity,
                ])
                  ? 700
                  : 400,
                color: concatStrings([
                  checkoutDetails?.apartment,
                  checkoutDetails?.userArea,
                  checkoutDetails?.userCity,
                ])
                  ? "#000000"
                  : "#BABABA",
              }}
            >
              {concatStrings([
                capitalizeFirstLetter(checkoutDetails?.apartment),
                capitalizeFirstLetter(checkoutDetails?.streetAddress),
                capitalizeFirstLetter(checkoutDetails?.userArea),
                capitalizeFirstLetter(checkoutDetails?.userCity),
                capitalizeFirstLetter(checkoutDetails?.userPostalCode),
                capitalizeFirstLetter(checkoutDetails?.userCountry),
              ])}
            </Typography>
          </Grid>
        )}

        {businessDetails.googleKey ? <GoogleProvider /> : <LeafletProvider />}

        <Grid item>
          <TextField
            name="apartment"
            variant="outlined"
            label="Apartment, Suite etc (Optional)"
            value={checkoutDetails?.apartment}
            textTransform={"capitalize"}
            onChange={handleInputChange}
            styles={{ ...outlinedInputStyle, m: "10px 0" }}
          />
        </Grid>

        <Grid container sx={addressDetailsStyles}>
          <Grid item>
            <TextField
              name="userCity"
              label="City"
              variant="outlined"
              value={checkoutDetails?.userCity}
              textTransform={"capitalize"}
              onChange={handleInputChange}
              styles={outlinedInputStyle}
              error={checkoutDetails?.validationError.userCity}
            />
          </Grid>

          <Grid item>
            <TextField
              name="userPostalCode"
              variant="outlined"
              label="Zip/Postal code"
              value={checkoutDetails?.userPostalCode}
              textTransform={"capitalize"}
              onChange={handleInputChange}
              styles={{ ...outlinedInputStyle, mt: { xs: "10px", sm: "0px" } }}
              error={checkoutDetails?.validationError.userPostalCode}
            />
          </Grid>
        </Grid>

        {radiusError && (
          <Typography sx={{ ...errorMessageStyles, mt: "10px" }}>
            {radiusError}
          </Typography>
        )}

        <Button
          loading={getCartCalculationsLoading}
          disabled={getCartCalculationsLoading}
          onClick={handleSaveLocation}
          sx={{ mt: "10px" }}
        >
          Add Address
        </Button>
      </Grid>
    </Modal>
  );
};

export default DeliveryDetailsModal;
