import { createSlice } from "@reduxjs/toolkit";
import {
  CheckoutFirebaseSettings,
  SocialLoginsType,
} from "checkout/index.types";

export interface FirebaseTypes {
  socialLogin: SocialLoginsType;
  settings: CheckoutFirebaseSettings;

  [key: string]: SocialLoginsType | CheckoutFirebaseSettings;
}

// initial state
const initialState: FirebaseTypes = {
  socialLogin: { google: false, facebook: false },
  settings: { expireLogs: 7 },
};

const Firebase = createSlice({
  name: "firebase",
  initialState,
  reducers: {
    bulkUpdateFirebaseStore(state, action) {
      return { ...state, ...action.payload.value };
    },
  },
});

export default Firebase.reducer;

export const { bulkUpdateFirebaseStore } = Firebase.actions;
