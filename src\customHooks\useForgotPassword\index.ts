import { useCallback } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { ErrorResponse } from "types";
import { userNotification } from "utils/helperFunctions";
import { forgotPassword } from "utils/apiConfig";
import { useSelector } from "store";

export const useForgotPassword = () => {
  const { businessId } = useSelector((state) => state.checkout);

  // ==============================|| API ||============================== //

  // API call to generate reset password link
  const [{ data, loading, error }, forgotPasswordAPICall] = useAxios(
    forgotPassword(businessId),
    { manual: true }
  );

  // ==============================|| handler functions ||============================== //

  // function to call forgot password API
  const callForgotPasswordAPI = useCallback(
    async (config: AxiosRequestConfig): Promise<boolean> => {
      try {
        // API call to generate reset password link
        const { data } = await forgotPasswordAPICall(config);

        // verify API response
        if (data?.status === 200) {
          userNotification(
            "Password reset link sent to your email. Check your inbox or spam.",
            "success"
          );

          return true;
        } else {
          userNotification("Failed to reset password!");

          return false;
        }
      } catch (error) {
        // handle API response if email does not exist
        if (
          (error as ErrorResponse)?.status === 400 &&
          (error as ErrorResponse)?.message?.includes("User not found")
        ) {
          // function to set message to notify user
          userNotification("Email does not exist. Please sign up");
        } else {
          // function to set message to notify user
          userNotification("Failed to reset password!");
        }

        return false;
      }
    },

    [forgotPasswordAPICall]
  );

  return {
    forgotPasswordData: data,
    forgotPasswordLoading: loading,
    forgotPasswordError: error,
    forgotPasswordAPICall: callForgotPasswordAPI,
  };
};
