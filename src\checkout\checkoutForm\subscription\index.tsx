import { useEffect, useRef, useState } from "react";

import {
  <PERSON><PERSON><PERSON>,
  Stack,
  FormControlLabel,
  Radio,
  RadioGroup,
  Paper,
  FormControl,
  Box,
  Divider,
} from "@mui/material";

import Button from "components/button";
import SubscriptionSchedule from "./subscriptionSchedule";
import {
  paperStyles,
  deliveryDetailsHeading,
  orderTypeRadio,
  subscriptionTypeLabel,
  deliveryAddress,
  labelStyles,
  editActionStyle,
  dividerStyle,
  noScheduleMessage,
  scheduleDetailsBox,
} from "../index.styles";
import { SelectedSubscriptionInfo } from "checkout/index.types";
import { subscriptionTypes, UIElementIds } from "utils/constant";
import {
  capitalizeFirstLetter,
  getLabelColor,
  validateSubscription,
  setInitialSubscription,
  formatSubscriptionSchedule,
} from "utils/helperFunctions";
import { dispatch, useSelector } from "store";
import { updateCheckoutStore } from "store/slices/checkout";

const Subscription = () => {
  const [toggleSubscriptionSchedule, setToggleSubscriptionSchedule] =
    useState<boolean>(false);

  // storing selected subscription key for day/date scroll
  const selectedScheduleKey = useRef<string>("");

  const checkoutDetails = useSelector((state) => state.checkout);
  const { branchDetails } = useSelector((state) => state.business);

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function to set subscription details
    setSubscriptionDetails(checkoutDetails?.subscription?.type);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // ==============================|| handler functions ||============================== //

  // function to set subscription details in store
  const setSubscriptionDetails = async (
    subscriptionType: string
  ): Promise<void> => {
    // function call to set initial day/date either current or scheduled from frontend
    const { subscriptionDetails, subscriptionKey }: SelectedSubscriptionInfo =
      setInitialSubscription(
        subscriptionType,
        branchDetails?.timeZone,
        checkoutDetails?.orderSchedule
      );

    // update subscription data in store
    await dispatch(
      updateCheckoutStore({
        type: "subscription",
        value: {
          type: subscriptionType,
          time: checkoutDetails?.orderSchedule?.time || "",
          details: subscriptionDetails,
        },
      })
    );

    // storing selected subscription key for day/date scroll
    selectedScheduleKey.current = subscriptionKey;
  };

  // function to handle the subscription type selection
  const handleSubscriptionTypeChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    // function to set subscription details (JSON) based on subscription type
    setSubscriptionDetails(event.target.value);
  };

  // function to open/close the subscription schedule modal
  const handleToggleSubscriptionSchedule = (): void => {
    setToggleSubscriptionSchedule((prevState) => !prevState);
  };

  // ==============================|| UI ||============================== //

  // function to display the subscription schedule
  const displaySelectedSchedule = (): JSX.Element => {
    // display the message if validation fails
    if (!validateSubscription(checkoutDetails?.subscription)) {
      return (
        <Typography sx={noScheduleMessage}>
          No date and time chosen yet
        </Typography>
      );
    }

    // display subscription schedule
    return (
      <Typography sx={deliveryAddress}>
        {formatSubscriptionSchedule(checkoutDetails?.subscription)}
      </Typography>
    );
  };

  // function to return subscription details UI
  const getSubscriptionDetailsUI = (): JSX.Element => {
    return (
      <Box sx={scheduleDetailsBox}>
        <Stack>
          <Typography
            id={UIElementIds?.subscription}
            sx={{
              ...labelStyles,

              color: getLabelColor(
                checkoutDetails?.validationError?.subscription
              ),
            }}
          >
            Subscription schedule*
          </Typography>

          {/* function to display the subscription schedule */}
          {displaySelectedSchedule()}
        </Stack>

        {/* toggle action buttons based on data validation */}
        {validateSubscription(checkoutDetails?.subscription) ? (
          <Typography
            onClick={handleToggleSubscriptionSchedule}
            sx={editActionStyle}
          >
            Edit
          </Typography>
        ) : (
          <Button
            onClick={handleToggleSubscriptionSchedule}
            sx={{ width: "fit-content" }}
          >
            Pick a slot
          </Button>
        )}
      </Box>
    );
  };

  return (
    <>
      <Paper elevation={0} sx={paperStyles}>
        <Stack width={"100%"} spacing={1.25}>
          <Typography sx={deliveryDetailsHeading}>Subscription type</Typography>

          <FormControl>
            <RadioGroup
              value={checkoutDetails?.subscription?.type}
              onChange={handleSubscriptionTypeChange}
              sx={orderTypeRadio}
            >
              {Object.values(subscriptionTypes)
                ?.slice(0, 2)
                ?.map((subscriptionType) => (
                  <FormControlLabel
                    key={subscriptionType}
                    value={subscriptionType}
                    control={<Radio />}
                    label={capitalizeFirstLetter(subscriptionType)}
                    disabled={checkoutDetails?.disableComponentFlag}
                    sx={subscriptionTypeLabel}
                  />
                ))}
            </RadioGroup>
          </FormControl>
        </Stack>

        <Divider sx={dividerStyle} />

        {/* set subscription UI if enabled */}
        {getSubscriptionDetailsUI()}
      </Paper>

      {/* subscription schedule modal */}
      {toggleSubscriptionSchedule && (
        <SubscriptionSchedule
          selectedScheduleKey={selectedScheduleKey?.current}
          toggleSubscriptionSchedule={toggleSubscriptionSchedule}
          handleToggleSubscriptionSchedule={handleToggleSubscriptionSchedule}
        />
      )}
    </>
  );
};

export default Subscription;
