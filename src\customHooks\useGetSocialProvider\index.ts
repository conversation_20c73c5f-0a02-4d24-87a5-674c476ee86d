import { useCallback } from "react";

import useAxios from "axios-hooks";

import { getSocialProvider } from "utils/apiConfig";
import { userNotification } from "utils/helperFunctions";

export const useGetSocialProvider = () => {
  // ==============================|| API ||============================== //

  // API call to get user's social login provider URL
  const [{ data, loading, error }, getSocialProviderAPICall] = useAxios(
    {},
    { manual: true }
  );

  // ========================|| handler functions ||======================== //

  // function to call get social login provider URL API
  const callGetSocialProviderAPI = useCallback(
    async (socialPlatform: string): Promise<void> => {
      try {
        // API call to get social login provider URL
        const { data } = await getSocialProviderAPICall(
          getSocialProvider(socialPlatform)
        );

        // verify API response
        if (data?.url) {
          // redirect user to social login page
          window.location.href = data?.url;
        }
      } catch (error) {
        // function to set message to notify user
        userNotification(
          `Could not connect with ${socialPlatform}. Please try again!`
        );
      }
    },
    [getSocialProviderAPICall]
  );

  return {
    getSocialProviderData: data,
    getSocialProviderLoading: loading,
    getSocialProviderError: error,
    getSocialProviderAPICall: callGetSocialProviderAPI,
  };
};
