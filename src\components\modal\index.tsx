import { Modal as MUIModal, Box } from "@mui/material";

import { modalBoxStyles, modalBoxParentStyles } from "components/index.styles";
import { ModalProps } from "components/index.types";

const Modal = ({ open, onClose, children, sx, modalSX }: ModalProps) => {
  return (
    <MUIModal open={open} onClose={onClose} sx={{ ...modalBoxParentStyles }}>
      <Box sx={{ ...modalBoxStyles, ...sx }}>
        <Box sx={{ ...modalSX }}>{children}</Box>
      </Box>
    </MUIModal>
  );
};

export default Modal;
