import { useCallback } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { ErrorResponse } from "types";
import { setUserAuthResponse, signUp } from "utils/apiConfig";
import { userNotification } from "utils/helperFunctions";
import { useSelector } from "store";

export const useSignUp = () => {
  const { businessId, paymentDetails } = useSelector((state) => state.checkout);

  // ==============================|| API ||============================== //

  // API call to sign up user
  const [{ data, loading, error }, signUpAPICall] = useAxios(
    signUp(businessId),
    { manual: true }
  );

  // ==============================|| handler functions ||============================== //

  // function to call sign up API
  const callSignUpAPI = useCallback(
    async (config: AxiosRequestConfig): Promise<boolean> => {
      try {
        // API call to sign up user
        const { data } = await signUpAPICall(config);

        // verify API response
        if (data?.status === 200 && data.result) {
          // function to map API response and update in store
          setUserAuthResponse(paymentDetails, data.result);

          // function to set message to notify user
          userNotification("Signed up successfully", "success");

          return true;
        }

        return false;
      } catch (error) {
        // handle API response if email already exists
        if (
          (error as ErrorResponse)?.status === 400 &&
          (error as ErrorResponse)?.message?.includes(
            "Email Address is already in use"
          )
        ) {
          // function to set message to notify user
          userNotification("Email already exists. Please login");
        } else {
          // function to set message to notify user
          userNotification("Failed to sign up. Please try again!");
        }

        return false;
      }
    },

    // eslint-disable-next-line react-hooks/exhaustive-deps
    [signUpAPICall]
  );

  return {
    signUpData: data,
    signUpLoading: loading,
    signUpError: error,
    signUpAPICall: callSignUpAPI,
  };
};
