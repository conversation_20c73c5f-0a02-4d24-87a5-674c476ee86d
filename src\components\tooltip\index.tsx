import { Tooltip as M<PERSON><PERSON>ooltip, Typography, Box, Zoom } from "@mui/material";
import { Info } from "@mui/icons-material";

import { tooltipIcon } from "components/index.styles";
import { tooltipStyles } from "checkout/checkoutForm/index.styles";
import { justifyAlignCenter } from "styles";
import { TooltipProps } from "components/index.types";

const Tooltip = ({
  id,
  open,
  message,
  children,
  disableHoverListener,
  disableFocusListener,
}: TooltipProps) => {
  return (
    <MUITooltip
      id={id}
      open={open}
      title={
        <Box sx={justifyAlignCenter}>
          <Info sx={tooltipIcon} />
          <Typography sx={tooltipStyles}>{message}</Typography>
        </Box>
      }
      arrow
      disableHoverListener={disableHoverListener}
      disableFocusListener={disableFocusListener}
      slots={{ transition: Zoom }}
    >
      {children}
    </MUITooltip>
  );
};

export default Tooltip;
