export interface BusinessInfoType {
  cartId: string;
  businessId: string;
  branchId: string;
  source: string;
  orderType: string;
  userLocation: { lat: string; lng: string };
  theme: {
    header_bg: string;
    header_font_color: string;
    body_bg: string;
    body_font_color: string;
    button_bg: string;
    button_font_color: string;
    button_radius: string;
  } | null;
  orderSchedule: OrderScheduleType;
  subscriptionType: string;
  addressDetails: {
    address: string;
    city: string;
    appartment: string;
    area: string;
    country: string;
    postalCode: string;
  };
  disableAddressUpdate: boolean;
  confirmationRedirectURL: string;
}

export interface DropDownList {
  label: string;
  value: string;
}

export type DaySuffix = "th" | "st" | "nd" | "rd";

export type ScrollDirection = "left" | "right";

export interface PaymentOptions {
  account_details: AccountDetails;
  account_id: string;
  enabled: boolean;
  method: string;
  name: string;
}

export type AccountDetails =
  | BankAccountDetails
  | StripeAccountDetails
  | PayfastAccountDetails
  | null;

export interface BankAccountDetails {
  accounts: BankAccountInfo[];
  note: string;
}

export interface BankAccountInfo {
  name: string;
  title: string;
  number: string;
  note: string;
}

export interface StripeAccountDetails {
  charges: number;
  charges_type: string;
  currency: string;
  preauth: boolean;
  preauth_charges: number;
  branchwise_payments: boolean;
  branchwise_settings: StripeBranchwiseSettings;
}

export interface StripeBranchwiseSettings {
  [branchId: string]: { account_id: string };
}

export interface PayfastAccountDetails {
  branchwise_payments: boolean;
  branchwise_settings: PayfastBranchwiseSettings;
}

export interface PayfastBranchwiseSettings {
  [branchId: string]: {
    user_name: string;
    secure_key: string;
    merchant_id: string;
    store_id: string;
  };
}

// branches types
export interface GetBranchesResponse {
  address: string;
  area: string;
  branch_id: string;
  city: string;
  country: string;
  delivery: string;
  email: string;
  lat: string;
  lng: string;
  location_address: string;
  note: string;
  phone: string;
  pickup: string;
  postcode: string;
  reservation: string;
  rname: string;
  service_charges: string;
  service_charges_type: string;
  settings: GetBranchesResponseSettings;
  tax_type: string;
  tax_value: string;
  time_zone: string;
  website: string;
}

export interface GetBranchesResponseSettings {
  cart: Cart;
}

export interface Cart {
  discount: string;
  minimum_spent: string;
  online_payment_tax: string;
  tax: string;
  tax_type: string;
}

// cart details types
export interface CartDetailsType {
  sourceFlag?: boolean;

  orderid: number;
  temp_order_id: string;
  total: number;
  tax: number;
  tax_value: number;
  discount: number;
  discount_value: number;

  gtotal: number;

  status: number;
  ordertype: string;
  delivery_charges: number;
  delivery_tax: number;
  delivery_tax_value: number;
  items: CartItemsType[];

  // not coming from get-cart but storing from cart-calc
  coupon_discount: number;
  coupon_discount_value: number;
  tip: number;
}

export interface CartItemsType {
  odetailid: number;
  orderid: number;
  id: null | number;
  type: null | string;
  dname: string;
  dqty: number;
  dprice: number;
  dtotal: number;
  item_level_grand_total: number;
  comment: null | string;
  option_set: string;
  menu_item_id: number;
  category_id: number;
  brand_id: number;
  eatout_user_id: number;
  delete_time: string | null;
  discount: string;
  item_level_discount_value: number;
  coupon_discount: number;
  coupon_discount_value: number;
  adjustment_amount: number;
  tax: string;
  item_level_tax_value: number;
  status: null | string;
  status_comment: null | string;
  weight_value: null | number;
  weight_unit: string;
  calculated_weight: number;
  orderstatus: number;
  product_code: string;
  category_name: string;
}

export interface LocationCoordinates {
  lat: number;
  lng: number;
}

export interface SiteThemeType {
  headerBackground: string;
  headerFontColor: string;
  bodyBackground: string;
  bodyFontColor: string;
  buttonBackground: string;
  buttonFontColor: string;
  buttonRadius: string;
}

export interface OrderScheduleType {
  date: string;
  time: string;
}

export type GenericMap = {
  [key: string]: string | number | GenericMap | any;
};

export interface ErrorResponse {
  message: string;
  status: number;
}

export interface RequestConfig {
  url: string;
  method: string;
}

export interface BranchCartSettings {
  cart: {
    tax: string;
    discount: string;
    tax_type: string;
    minimum_spent: string;
    online_payment_tax: string;
  };
}

export interface BusinessCheckoutSettings {
  button_text: string;
  force_user_auth: boolean;
}

export interface CheckoutSettings {
  submitFormButtonText: string;
  forceUserAuth: boolean;
}
