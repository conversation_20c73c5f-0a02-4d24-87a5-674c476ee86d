import { useCallback, useEffect } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { CartDetailsType, ErrorResponse } from "types";
import {
  getCartCalculations,
  setCartCalculationResponse,
} from "utils/apiConfig";
import { setDisableComponentFlag } from "utils/helperFunctions";

export const useCartCalculation = () => {
  // ==============================|| API ||============================== //

  // API call to get cart calculations
  const [{ data, loading, error }, getCartCalculationsAPICall] = useAxios(
    getCartCalculations(),
    { manual: true }
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function to set the API loading state in store to disable components
    setDisableComponentFlag(loading);
  }, [loading]);

  // ==============================|| handler functions ||============================== //

  // function to call cart calculations API
  const callCartCalculationAPI = useCallback(
    async (
      config: AxiosRequestConfig,
      // passing as prop to get the update state on calling calculation API initially
      cartDetails: CartDetailsType
    ): Promise<boolean | ErrorResponse> => {
      try {
        // API call to calculate cart
        const { data: calculatedCartData } = await getCartCalculationsAPICall(
          config
        );

        // verify API response
        if (calculatedCartData?.status === 200 && calculatedCartData?.result) {
          // function to map API response and update in store
          setCartCalculationResponse(cartDetails, calculatedCartData?.result);

          return true;
        }

        return false;
      } catch (error) {
        return error as ErrorResponse;
      }
    },

    [getCartCalculationsAPICall]
  );

  return {
    getCartCalculationsData: data,
    getCartCalculationsLoading: loading,
    getCartCalculationsError: error,
    getCartCalculationsAPICall: callCartCalculationAPI,
  };
};
