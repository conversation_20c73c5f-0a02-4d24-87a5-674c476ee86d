import { useCallback } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { getPickupSlots } from "utils/apiConfig";

export const useGetPickupHours = () => {
  // ==============================|| API ||============================== //

  // API call to get time slots
  const [{ data, loading, error }, getPickupHoursAPICall] = useAxios(
    getPickupSlots(),
    { manual: true }
  );

  // ==============================|| handler functions ||============================== //

  // function to call get pickup hours API
  const callGetPickupHoursAPI = useCallback(
    async (config: AxiosRequestConfig): Promise<string[]> => {
      try {
        // API call to get pickup hours
        const { data } = await getPickupHoursAPICall(config);

        // verify API response
        if (data?.status === "200" && Array.isArray(data.slots)) {
          return data.slots;
        }

        return [];
      } catch (error) {
        return [];
      }
    },

    [getPickupHoursAPICall]
  );

  return {
    data,
    loading,
    error,
    getPickupHoursAPICall: callGetPickupHoursAPI,
  };
};
