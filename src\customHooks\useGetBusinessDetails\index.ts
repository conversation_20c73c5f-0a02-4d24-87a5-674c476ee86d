import { useCallback } from "react";

import useAxios from "axios-hooks";

import { PaymentMethodType, PaymentOrderDetails } from "checkout/index.types";
import { initialPaymentMethod, initialTip } from "utils/constant";
import { getBusinessData } from "utils/apiConfig";
import {
  getBranchOrderTypes,
  getCheckoutSettings,
  getMinSpend,
  getPaymentMethods,
} from "utils/helperFunctions";
import { dispatch, useSelector } from "store";
import { bulkUpdateCheckoutStore } from "store/slices/checkout";
import { updateBusinessStore } from "store/slices/business";

export const useGetBusinessDetails = () => {
  const checkoutDetails = useSelector((state) => state.checkout);

  // ==============================|| API ||============================== //

  // API call to get business details
  const [{ data, loading, error }, getBusinessDetailsAPICall] = useAxios(
    getBusinessData(checkoutDetails?.businessId, checkoutDetails?.branchId),
    { manual: true }
  );

  // ==============================|| handler functions ||============================== //

  // function to call get business details API
  const callGetBusinessDetailsAPI = useCallback(async (): Promise<
    PaymentOrderDetails | boolean
  > => {
    let paymentMethodsList: PaymentMethodType[] = [];
    let orderTypeList: string[] = [];

    try {
      // API call to get business details
      const { data } = await getBusinessDetailsAPICall();

      // verify API response
      if (
        data?.status === 200 &&
        data.result &&
        data.result?.branches?.length
      ) {
        const {
          name,
          currencycode,
          branches,
          decimal_places,
          payment_options,
          settings,
          address_api,
        } = data.result;

        // function to get modified payment methods
        paymentMethodsList = getPaymentMethods(payment_options, currencycode);

        // set business settings in business store
        dispatch(
          updateBusinessStore({
            type: "businessDetails",
            value: {
              name,
              currencyCode: currencycode,
              decimalPlaces: decimal_places || 0,
              paymentMethods: paymentMethodsList,
              futureOrder: settings?.future_order || 0,
              tip: settings?.gratuity ? settings.gratuity : initialTip,
              googleKey: address_api || "",
              checkoutSettings: getCheckoutSettings(settings?.checkout),
            },
          })
        );

        // set settings of selected branch
        const {
          address,
          country,
          city,
          location,
          lat,
          lng,
          delivery,
          pickup,
          delivery_settings,
          time_zone,
          settings: branchSettings,
        } = branches[0];

        // set branch details in business store
        dispatch(
          updateBusinessStore({
            type: "branchDetails",
            value: {
              address,
              country,
              city,
              area: location,
              lat,
              lng,
              deliverySettings: delivery_settings
                ? JSON.parse(delivery_settings)
                : null,
              timeZone: time_zone,
              minSpend: getMinSpend(branchSettings),
            },
          })
        );

        // function to set order type list from branch settings
        orderTypeList = getBranchOrderTypes(
          checkoutDetails?.orderType,
          delivery,
          pickup
        );

        // set data in store
        dispatch(
          bulkUpdateCheckoutStore({
            type: "",
            value: {
              paymentType: paymentMethodsList[0] || initialPaymentMethod,
              orderTypeList,
              orderType: orderTypeList[0] || checkoutDetails?.orderType,
            },
          })
        );

        // return order type & payment methods to pass in calculation API
        return {
          orderType: orderTypeList[0] || checkoutDetails?.orderType,
          paymentType: paymentMethodsList[0] || initialPaymentMethod,
        };
      }

      return false;
    } catch (error) {
      return false;
    }
  }, [getBusinessDetailsAPICall, checkoutDetails?.orderType]);

  return {
    getBusinessDetailsData: data,
    loading,
    error,
    getBusinessDetailsAPICall: callGetBusinessDetailsAPI,
  };
};
