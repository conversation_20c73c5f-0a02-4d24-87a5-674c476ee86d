import React, { useState, useEffect } from "react";

import { Grid } from "@mui/material";
import { useLoadScript } from "@react-google-maps/api";

import TextField from "components/textField";
import { getCountryCode } from "utils/helperFunctions";
import { dispatch, useSelector } from "store";
import { bulkUpdateCheckoutStore } from "store/slices/checkout";

const GoogleProvider: React.FC = () => {
  const [searchInput, setSearchInput] = useState<string>("");

  const { businessDetails, branchDetails } = useSelector(
    (state) => state.business
  );
  const { streetAddress, validationError } = useSelector(
    (state) => state.checkout
  );

  const { isLoaded } = useLoadScript({
    googleMapsApiKey: businessDetails.googleKey,
    libraries: ["places"],
  });

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    if (isLoaded) {
      setSearchInput(streetAddress);

      const autocomplete = new google.maps.places.Autocomplete(
        document.getElementById("searchAddress")! as HTMLInputElement,
        {
          // restrict results to a specific country
          componentRestrictions: {
            country: getCountryCode(branchDetails.country),
          },
        }
      );

      // debounced function to handle place change
      const handlePlaceChange = () => {
        const place = autocomplete.getPlace();

        if (!place?.geometry?.location) {
          return;
        }

        const location = place.geometry.location;

        let street = "";
        let area = "";
        let city = "";
        let country = "";
        let countryCode = "";
        let postalCode = "";

        place?.address_components?.forEach((component) => {
          if (
            component.types.includes("premise") ||
            component.types.includes("street_number") ||
            component.types.includes("route")
          ) {
            street = `${street}${street && component.long_name ? " " : ""}${
              component.long_name
            }`;
          }

          if (component.types.includes("locality")) {
            city = component.long_name;
          }
          if (component.types.includes("country")) {
            country = component.long_name;
            countryCode = component.short_name;
          }
          if (component.types.includes("postal_code")) {
            postalCode = component.long_name;

            if (postalCode) {
              area = " ";
            }
          }
        });

        setSearchInput(street);

        dispatch(
          bulkUpdateCheckoutStore({
            type: "",
            value: {
              userArea: area,
              userCountry: country,
              userCity: city,
              userCountryCode: countryCode,
              userPostalCode: postalCode,

              streetAddress: street,
              userLocation: location.toJSON(),

              validationError: {
                ...validationError,

                userCity: city ? "" : validationError.userCity,
                userPostalCode: postalCode
                  ? ""
                  : validationError.userPostalCode,
              },
            },
          })
        );
      };

      autocomplete.addListener("place_changed", handlePlaceChange);
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoaded]);

  // ==============================|| UI ||============================== //

  return (
    <Grid item>
      <TextField
        id="searchAddress"
        name="searchAddress"
        placeholder="Enter delivery address"
        textTransform={"capitalize"}
        value={searchInput}
        onChange={(e) => setSearchInput(e.target.value)}
      />
    </Grid>
  );
};

export default GoogleProvider;
