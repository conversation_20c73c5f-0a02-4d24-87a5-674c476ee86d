import { useState } from "react";

import { <PERSON>, Divider, Stack, Typography } from "@mui/material";

import Button from "components/button";
import UpdateOrderSchedule from "./updateOrderSchedule";
import {
  scheduleDetailsBox,
  labelStyles,
  editActionStyle,
  deliveryAddress,
  noScheduleMessage,
} from "checkout/checkoutForm/index.styles";
import { borderColor, UIElementIds } from "utils/constant";
import {
  formatOrderSchedule,
  getLabelColor,
  validateOrderSchedule,
} from "utils/helperFunctions";
import { useSelector } from "store";

const OrderSchedule = () => {
  const [toggleUpdateSchedule, setToggleUpdateSchedule] =
    useState<boolean>(false);

  const { orderSchedule, validationError, orderType } = useSelector(
    (state) => state.checkout
  );
  const { branchDetails } = useSelector((state) => state.business);

  // ==============================|| handler functions ||============================== //

  // function to open/close the schedule modal
  const handleToggleUpdateSchedule = (): void => {
    setToggleUpdateSchedule((prevState) => !prevState);
  };

  // ==============================|| UI ||============================== //

  // function to display the order schedule
  const displaySelectedSchedule = (): JSX.Element => {
    // display the message if validation fails
    if (!validateOrderSchedule(orderSchedule)) {
      return (
        <Typography sx={noScheduleMessage}>
          No date and time chosen yet
        </Typography>
      );
    }

    // display order schedule
    return (
      <Typography sx={deliveryAddress}>
        {formatOrderSchedule(orderSchedule, branchDetails?.timeZone)}
      </Typography>
    );
  };

  return (
    <>
      <Box sx={scheduleDetailsBox}>
        <Stack>
          <Typography
            id={UIElementIds?.orderSchedule}
            sx={{
              ...labelStyles,

              color: getLabelColor(validationError.orderSchedule),
            }}
          >
            {`Schedule ${orderType}*`}
          </Typography>

          {/* function to display the order schedule */}
          {displaySelectedSchedule()}
        </Stack>

        {/* toggle action buttons based on data validation */}
        {validateOrderSchedule(orderSchedule) ? (
          <Typography onClick={handleToggleUpdateSchedule} sx={editActionStyle}>
            Edit
          </Typography>
        ) : (
          <Button
            onClick={handleToggleUpdateSchedule}
            sx={{ width: "fit-content" }}
          >
            Pick a slot
          </Button>
        )}
      </Box>

      <Divider sx={{ borderColor }} />

      {/* update order schedule modal (not rendering conditionally to auto select in pickup now case) */}
      <UpdateOrderSchedule
        toggleUpdateSchedule={toggleUpdateSchedule}
        handleToggleUpdateSchedule={handleToggleUpdateSchedule}
      />
    </>
  );
};

export default OrderSchedule;
