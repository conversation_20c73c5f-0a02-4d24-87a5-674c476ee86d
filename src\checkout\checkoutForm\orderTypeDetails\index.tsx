import {
  Divider,
  FormControl,
  FormControlLabel,
  Paper,
  Radio,
  RadioGroup,
  Stack,
  Typography,
} from "@mui/material";

import { useGetCart } from "customHooks/useGetCart";
import { useCartCalculation } from "customHooks/useCartCalculation";

import UserDetail from "../userDetails";
import DeliveryDetails from "../deliveryDetails";
import PickupDetails from "../pickupDetails";
import SpecialInstructions from "../specialInstructions";
import OrderSchedule from "../orderSchedule";

import {
  deliveryDetailsHeading,
  paperStyles,
  orderTypeRadio,
  dividerStyle,
} from "../index.styles";
import { CartDetailsType, ErrorResponse } from "types";
import { orderTypes, scheduleTypes } from "utils/constant";
import { capitalizeFirstLetter } from "utils/helperFunctions";
import { getCartCalculationPayload } from "utils/apiConfig";
import { dispatch, useSelector } from "store";
import {
  bulkUpdateCheckoutStore,
  updateCheckoutStore,
} from "store/slices/checkout";

const OrderTypeDetails = () => {
  const checkoutDetails = useSelector((state) => state.checkout);
  const { businessDetails } = useSelector((state) => state.business);

  // ==============================|| custom hooks ||============================== //

  // custom hook to call get cart API
  const { getCartLoading, getCartAPICall } = useGetCart();

  // custom hook to call cart calculation API
  const { getCartCalculationsAPICall } = useCartCalculation();

  // ==============================|| handler functions ||============================== //

  // function to call get cart details API
  const getCartDetails = async (): Promise<void> => {
    // API call to get cart details
    const result: CartDetailsType | void = await getCartAPICall();

    // verify API response
    if (result) {
      //  set cart details in store
      dispatch(updateCheckoutStore({ type: "cartDetails", value: result }));
    }
  };

  // function to handle the cart calculation API
  const callCartCalculationAPI = async (orderType: string): Promise<void> => {
    // API call to calculate cart
    const result: boolean | ErrorResponse = await getCartCalculationsAPICall(
      getCartCalculationPayload(
        checkoutDetails,
        checkoutDetails?.paymentType?.value,
        orderType,
        checkoutDetails?.couponCode,
        (businessDetails?.tip[orderType] &&
          checkoutDetails?.cartDetails?.tip) ||
          0
      ),
      checkoutDetails?.cartDetails
    );

    // check if response is of error type
    if (typeof result !== "boolean") {
      // call get cart API on failure of calculations API to reset calculations according to order type (e-g tip, delivery charges...)
      getCartDetails();

      // set userLocationFlag => true to render error message in case of delivery
      dispatch(
        updateCheckoutStore({
          type: "userLocationFlag",
          value: orderType === orderTypes?.DELIVERY,
        })
      );
    }
  };

  // function to handle the order type selection
  const handleOrderTypeChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    // set order type data to store
    dispatch(
      bulkUpdateCheckoutStore({
        type: "",
        value: { orderType: event.target.value, userLocationFlag: false },
      })
    );

    // call cart calculation API to update calculations according to order type
    callCartCalculationAPI(event.target.value);
  };

  // ==============================|| UI ||============================== //

  // function to return component according to order type
  const setOrderTypeUI = (): JSX.Element => {
    return checkoutDetails?.orderType === orderTypes?.DELIVERY ? (
      <DeliveryDetails />
    ) : (
      <PickupDetails />
    );
  };

  // display order type tabs if don't get order type from cross technology
  const setOrderTypeRadio = (): JSX.Element => {
    return checkoutDetails?.orderTypeList?.length ? (
      <>
        <Typography component="strong" sx={deliveryDetailsHeading}>
          Order type
        </Typography>

        <FormControl component="fieldset">
          <RadioGroup
            value={checkoutDetails?.orderType}
            onChange={handleOrderTypeChange}
            sx={orderTypeRadio}
          >
            {checkoutDetails?.orderTypeList?.map((orderType) => (
              <FormControlLabel
                key={orderType}
                value={orderType}
                control={<Radio />}
                label={capitalizeFirstLetter(orderType)}
                disabled={
                  checkoutDetails?.disableComponentFlag || getCartLoading
                }
              />
            ))}
          </RadioGroup>
        </FormControl>

        <Divider sx={dividerStyle} />
      </>
    ) : (
      <></>
    );
  };

  // return the order scheduler/subscription component
  const getOrderScheduler = (): JSX.Element => {
    // return if subscription is enabled
    if (checkoutDetails?.subscription?.type) {
      return <></>;
    }

    /**
     * returns component for pickup
     * returns component for delivery
     * => if future order is active
     * => if subscriptionType is 'schedule' (to schedule order)
     */
    if (
      checkoutDetails?.orderType === orderTypes?.PICK_UP ||
      (checkoutDetails?.orderType === orderTypes?.DELIVERY &&
        (businessDetails?.futureOrder ||
          businessInfo?.subscriptionType === scheduleTypes?.LATER))
    ) {
      return <OrderSchedule />;
    }

    return <></>;
  };

  return (
    <Paper elevation={0} sx={paperStyles}>
      <Stack width={"100%"} spacing={2.5}>
        {/* order type tabs UI */}
        {setOrderTypeRadio()}

        <Typography component="strong" sx={deliveryDetailsHeading}>
          {`${capitalizeFirstLetter(checkoutDetails?.orderType)} details`}
        </Typography>

        {/* user personal info */}
        <UserDetail />

        {/* toggle UI based on order type */}
        {setOrderTypeUI()}

        {/* schedule order for future */}
        {getOrderScheduler()}

        {/* note component */}
        <SpecialInstructions />
      </Stack>
    </Paper>
  );
};

export default OrderTypeDetails;
