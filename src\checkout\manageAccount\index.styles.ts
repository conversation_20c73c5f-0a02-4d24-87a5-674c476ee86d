import { justifyEnd, justifyAlignCenter, pointer } from "styles";

export const createAccount = {
  fontSize: "14px",
  fontWeight: "600",
  color: "#000",
  textDecorationLine: "underline",
  ...pointer,
};

export const socialLoginsStack = { ...justifyAlignCenter, m: "0px -10px" };

export const socialButtonsBox = {
  display: "flex",
  flex: { xs: "0 0 100%", md: "0 0 33.333%" },
  width: { xs: "100%", md: "33.333%" },
  p: "0px 10px !important",
};

export const authButtonStyles = {
  p: "15px 10px !important",
  fontSize: "12px",
  fontWeight: "400",
};

export const googleButtonStyles = {
  ...authButtonStyles,

  "&:hover": { background: "#E74133" },
  color: "#FFFFFF",
  background: "#E74133",
};

export const facebookButtonStyles = {
  ...authButtonStyles,

  "&:hover": { background: "#1976D2" },
  color: "#FFFFFF",
  background: "#1976D2",
};

export const emailButtonStyles = {
  ...authButtonStyles,

  color: "#fff",
  background: "#000",

  "&:hover": { background: "#000" },
};

export const outlinedButtonStyles = {
  color: "#000",
  backgroundColor: "none",
  border: "1px solid #000",

  "&:hover": { backgroundColor: "unset", border: "1px solid #000" },
};

export const authIconStyles = { width: "25px", height: "18px" };

export const mb5 = { mb: "5px" };
export const mb10 = { mb: "10px" };
export const mb15 = { mb: "15px" };
export const mb20 = { mb: "20px" };

export const forgotPasswordBox = { ...justifyEnd, ...mb20 };

export const forgotPassword = {
  fontSize: "12px",
  fontWeight: "400",
  width: "fit-content",
  cursor: "pointer",
};

export const authTypoStyle = {
  fontSize: "18px",
  fontWeight: "700",
  ...justifyAlignCenter,
  ...mb20,
};

export const authTabs = {
  width: "100%",
  backgroundColor: "#EEEEEE",
  borderRadius: "40px",
  p: "8px",

  "& .MuiTabs-indicator": { display: "none" },
  "& .MuiTab-root": { textTransform: "none !important" },

  "& .Mui-selected": {
    color: "#000000 !important",
    backgroundColor: "#FFFFFF",
    borderRadius: "40px",
  },
};

export const authParentStack = {
  maxWidth: { xs: "unset", md: "650px" },
  m: { xs: "unset", md: "0 auto" },
  alignItems: "center",
};
