import { Container, Box, Typography, Stack } from "@mui/material";
import { ShoppingCartCheckout } from "@mui/icons-material";
import moment from "moment";

import { usePayMobPayment } from "customHooks/usePayMobPayment";
import { useAlfalahPayment } from "customHooks/useAlfalahPayment";
import { usePayFastPayment } from "customHooks/usePayFastPayment";

import Button from "components/button";
import {
  cartIconStyles,
  confirmationChildBox,
  confirmationComponent,
  confirmationOrder,
  confirmationId,
  confirmationOrderText,
  confirmationParentBox,
  orderDetailStyles,
} from "checkout/checkoutForm/index.styles";
import { displayRow, retryButtonMargin } from "styles";
import {
  dateTimeFormats,
  paymentMethods,
  transactionFailure,
} from "utils/constant";
import {
  capitalizeFirstLetter,
  formatSubscriptionSchedule,
  getAlfalahReturnURL,
  handleBackNavigation,
} from "utils/helperFunctions";
import { paymentChargeAPIPayload } from "utils/apiConfig";
import { useSelector } from "store";

const ConfirmationPage = () => {
  const {
    businessId,
    branchId,
    email,
    source,
    siteUrl,
    cartDetails,
    orderType,
    paymentType,
    orderSchedule,
    subscription,
    checkoutProcessState,
  } = useSelector((state) => state.checkout);
  const { businessDetails } = useSelector((state) => state.business);

  // ==============================|| custom hooks ||============================== //

  // custom hook to call get payMob payment token API
  const { loading: getPayMobTokenLoading, getPayMobTokenAPICall } =
    usePayMobPayment();

  // custom hook to get bank alfalah payment session
  const { loading: getAlfalahSessionLoading, getAlfalahSessionAPICall } =
    useAlfalahPayment();

  // custom hook to call get payFast payment token API
  const { loading: getPayFastTokenLoading, getPayFastTokenAPICall } =
    usePayFastPayment();

  // ==============================|| handler functions ||============================== //

  // function to call API to reload payment method
  const recallPaymentMethod = async (): Promise<void> => {
    if (
      paymentType.type?.toLowerCase() === paymentMethods?.BANK_ALFALAH?.value
    ) {
      // API call to get alfalah session
      await getAlfalahSessionAPICall(
        paymentChargeAPIPayload(
          checkoutProcessState?.id,
          businessId,
          branchId,
          email,
          cartDetails.gtotal,
          businessDetails?.currencyCode,
          paymentMethods?.BANK_ALFALAH?.value,
          "",
          getAlfalahReturnURL(
            siteUrl,
            checkoutProcessState?.id,
            paymentMethods?.BANK_ALFALAH?.id
          ),
          "",
          ""
        ),
        checkoutProcessState?.id
      );
    } else if (
      paymentType.type?.toLowerCase() === paymentMethods?.PAY_MOB?.value
    ) {
      // API call to get payMob token
      await getPayMobTokenAPICall(
        paymentChargeAPIPayload(
          checkoutProcessState?.id,
          businessId,
          branchId,
          email,
          cartDetails.gtotal,
          businessDetails?.currencyCode,
          paymentMethods?.PAY_MOB?.value,
          "",
          "",
          "",
          ""
        ),
        checkoutProcessState?.id
      );
    } else if (
      paymentType.type?.toLowerCase() === paymentMethods?.PAY_FAST?.value
    ) {
      // API call to get payFast payment token
      await getPayFastTokenAPICall(
        paymentChargeAPIPayload(
          checkoutProcessState?.id,
          businessId,
          branchId,
          email,
          cartDetails.gtotal,
          businessDetails?.currencyCode,
          paymentMethods?.PAY_FAST?.value,
          "",
          "",
          "",
          ""
        ),
        checkoutProcessState?.id
      );
    }
  };

  // returns boolean depending upon the API call status
  const toggleButtonDisable = (): boolean => {
    return (
      getPayMobTokenLoading ||
      getAlfalahSessionLoading ||
      getPayFastTokenLoading
    );
  };

  // ==============================|| UI ||============================== //

  // function to display the order schedule
  const displayOrderDetails = (): JSX.Element => {
    let schedule: string = "";

    /**
     * if => subscription details
     * else if => scheduled order details
     * else => message for deliver now
     */
    if (subscription?.type) {
      schedule = `${capitalizeFirstLetter(
        subscription?.type
      )} on ${formatSubscriptionSchedule(subscription)}`;
    } else if (orderSchedule?.date) {
      schedule = `${moment(
        orderSchedule?.date,
        `${dateTimeFormats?.DATE}`
      )?.format(`${dateTimeFormats?.DAY_MONTH}`)} ${moment(
        orderSchedule?.time,
        `${dateTimeFormats?.TIME_MERIDIAN}`
      )?.format(`${dateTimeFormats?.MINUTES_TIME_MERIDIAN}`)}`;
    } else {
      schedule = "You order will be ready shortly";
    }

    return schedule ? (
      <Typography sx={orderDetailStyles}>
        {`Scheduled time: ${schedule}`}
      </Typography>
    ) : (
      <></>
    );
  };

  // function to return the home button
  const getHomeButton = (): JSX.Element => {
    return (
      <Button onClick={() => handleBackNavigation(source, siteUrl)}>
        Back to home
      </Button>
    );
  };

  return (
    <Box sx={confirmationComponent}>
      <Container maxWidth="sm">
        <Box sx={confirmationParentBox}>
          <Box sx={confirmationChildBox}>
            <Box>
              <ShoppingCartCheckout sx={cartIconStyles} />

              {checkoutProcessState?.id > 0 && (
                <Typography component="h1" sx={confirmationId}>
                  {`${subscription?.type ? "Subscription" : "Order"} ID # ${
                    checkoutProcessState?.id
                  }`}
                </Typography>
              )}

              <Typography
                component="h1"
                sx={{
                  ...confirmationOrder,
                  color:
                    checkoutProcessState?.statusMessage === transactionFailure
                      ? "red"
                      : "black",
                }}
              >
                {checkoutProcessState?.statusMessage}
              </Typography>

              <Typography sx={orderDetailStyles}>
                {`Order type: ${capitalizeFirstLetter(orderType)}`}
              </Typography>

              {/* function call to display order details */}
              {displayOrderDetails()}

              <Typography variant="body1" sx={confirmationOrderText}>
                {checkoutProcessState?.message}
              </Typography>
            </Box>

            {checkoutProcessState?.reloadPaymentMethod ? (
              <>
                <Stack sx={displayRow}>
                  {getHomeButton()}

                  <Button
                    loading={getPayMobTokenLoading}
                    disabled={toggleButtonDisable()}
                    sx={retryButtonMargin}
                    onClick={recallPaymentMethod}
                  >
                    Try again
                  </Button>
                </Stack>
              </>
            ) : (
              <>{getHomeButton()}</>
            )}
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

export default ConfirmationPage;
