{"name": "checkout", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.13.0", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^5.15.3", "@mui/material": "^5.16.6", "@mui/x-date-pickers": "^7.11.1", "@react-google-maps/api": "^2.19.3", "@reduxjs/toolkit": "^2.0.1", "@sentry/react": "^8.47.0", "@stripe/react-stripe-js": "^2.5.0", "@stripe/stripe-js": "^3.0.2", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.70", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "axios": "^1.6.5", "axios-hooks": "^5.0.2", "dayjs": "^1.11.12", "env-cmd": "^10.1.0", "firebase": "^11.0.2", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "leaflet-geosearch": "^3.11.0", "libphonenumber-js": "^1.11.15", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "react": "^18.2.0", "react-app-rewired": "^2.2.1", "react-dom": "^18.2.0", "react-international-phone": "^4.2.3", "react-leaflet": "^4.2.1", "react-redux": "^9.1.0", "react-router-dom": "^7.0.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-app-rewired start", "dev": "env-cmd -f .env.dev react-app-rewired start", "prod": "env-cmd -f .env.prod react-app-rewired start", "build:staging": "env-cmd -f .env.dev react-app-rewired build", "build:production": "env-cmd -f .env.prod react-app-rewired build", "build": "react-app-rewired build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-transform-private-property-in-object": "^7.23.4", "@types/js-cookie": "^3.0.6", "@types/jwt-decode": "^2.2.1", "@types/leaflet": "^1.9.8", "@types/lodash": "^4.14.202"}}