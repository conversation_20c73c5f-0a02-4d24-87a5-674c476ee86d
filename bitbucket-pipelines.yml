image: node:22.14.0

pipelines:
  branches:
    develop:
      - step:
          size: 2x
          name: Build and Test
          caches:
            - node
          script:
            - npm install
            - npm run build:staging
          artifacts:
            - build/**
      - step:
          name: Deploy to Staging
          deployment: Staging
          # trigger: manual  # Uncomment to make this a manual deployment.
          script:
            - apt-get update -q && apt-get install -y -q python-dev-is-python3
            - curl -O https://bootstrap.pypa.io/pip/2.7/get-pip.py
            - python get-pip.py
            - pip install -q awscli
            - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
            - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
            # Sync build directory to S3 bucket
            - aws s3 sync --delete ./build s3://ordrz-checkout/staging --acl public-read
            # Invalidate CloudFront cache
            - aws cloudfront create-invalidation --distribution-id E1SQZ5Y138H753 --paths "/*"

    master:
      - step:
          size: 2x
          name: Build and Test
          caches:
            - node
          script:
            - npm install
            - npm run build:production
          artifacts:
            - build/**
      - step:
          name: Deploy to Production
          deployment: Production
          # trigger: manual  # Uncomment to make this a manual deployment.
          script:
            - apt-get update -q && apt-get install -y -q python-dev-is-python3
            - curl -O https://bootstrap.pypa.io/pip/2.7/get-pip.py
            - python get-pip.py
            - pip install -q awscli
            - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
            - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
            # Sync build directory to S3 bucket
            - aws s3 sync --delete ./build s3://ordrz-checkout/prod --acl public-read
            # Invalidate CloudFront cache
            - aws cloudfront create-invalidation --distribution-id E2VNU156FN0KH7 --paths "/*"
