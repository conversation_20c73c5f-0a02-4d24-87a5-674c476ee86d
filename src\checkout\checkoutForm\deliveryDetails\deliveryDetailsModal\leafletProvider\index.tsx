import { SetStateAction, useEffect, useRef, useState } from "react";

import {
  Grid,
  ClickAwayListener,
  List,
  ListItem,
  ListItemText,
} from "@mui/material";
import { OpenStreetMapProvider } from "leaflet-geosearch";
import { SearchResult } from "leaflet-geosearch/dist/providers/provider";
import { RawResult } from "leaflet-geosearch/dist/providers/openStreetMapProvider";
import { debounce } from "lodash";

import TextField from "components/textField";

import { outlinedInputStyle } from "styles";
import { debouncePeriod } from "utils/constant";
import { concatStrings, getCountryCode } from "utils/helperFunctions";

import { dispatch, useSelector } from "store";
import {
  bulkUpdateCheckoutStore,
  updateCheckoutStore,
} from "store/slices/checkout";

const LeafletProvider: React.FC = () => {
  const [searchInput, setSearchInput] = useState<string>("");
  const [searchResults, setSearchResults] = useState<SearchResult<RawResult>[]>(
    []
  );

  const { userArea, userCity, validationError } = useSelector(
    (state) => state.checkout
  );
  const { branchDetails } = useSelector((state) => state.business);

  const mapProvider = new OpenStreetMapProvider({
    params: {
      // limit search results to the Netherlands
      countrycodes: getCountryCode(branchDetails.country),
      addressdetails: 1,
    },
  });

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    setSearchInput(concatStrings([userArea, userCity]));

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // ==============================|| handler functions ||============================== //

  // function to get user address details from lat lng
  const getAddress = async (coords: any) => {
    const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${coords.lat}&lon=${coords.lng}&zoom=13&addressdetails=1`;

    // fetch address details by providing lat lng
    fetch(url)
      .then((response) => response.json())
      .then((data) => {
        const addressDetails = data?.address;

        // set address details in store
        if (addressDetails) {
          setSearchInput(
            concatStrings([
              addressDetails.suburb || data.name || "",
              addressDetails?.city || addressDetails?.district || "",
            ])
          );

          dispatch(
            bulkUpdateCheckoutStore({
              type: "userAddressDetails",
              value: {
                userArea: " ",
                userCountry: addressDetails?.country || "",
                userCity:
                  addressDetails?.city || addressDetails?.district || "",
                userCountryCode: addressDetails?.country_code || "",
                userPostalCode: addressDetails?.postcode || "",
                userState: addressDetails?.state || "",

                validationError: {
                  ...validationError,

                  userCity:
                    addressDetails?.city || addressDetails?.district
                      ? ""
                      : validationError.userCity,
                  userPostalCode: addressDetails?.postcode
                    ? ""
                    : validationError.userPostalCode,
                },
              },
            })
          );
        }
      })
      .catch((error) => console.error("Error:", error));
  };

  const handleAddressSearch = (event: {
    target: { value: SetStateAction<string> };
  }) => {
    setSearchInput(event.target.value);

    findSearchedAddress(event.target.value);
  };

  /**
   * using useRef() hook to store the debounced function
   * instead function will be recreated on every render of key stroke
   */
  const findSearchedAddress = useRef(
    debounce(async (address) => {
      try {
        const searchedResults = await mapProvider.search({ query: address });

        if (Array.isArray(searchedResults) && searchedResults?.length) {
          setSearchResults(searchedResults);
        } else {
          setSearchResults([]);
        }
      } catch (error) {
        dispatch(updateCheckoutStore({ type: "apiCallError", value: true }));
      }
    }, debouncePeriod)
  ).current;

  // ==============================|| UI ||============================== //

  return (
    <Grid item>
      <TextField
        name="searchAddress"
        variant="outlined"
        label="Enter delivery address"
        textTransform={"capitalize"}
        value={searchInput}
        onChange={handleAddressSearch}
        styles={outlinedInputStyle}
      />

      {searchResults?.length > 0 && (
        <ClickAwayListener onClickAway={() => setSearchResults([])}>
          <List
            sx={{
              background: "#fff",
              p: "0",
              borderRadius: "6px",
              overflow: "auto",
              maxHeight: "150px",
            }}
          >
            {searchResults.map((result: any, index: number) => (
              <ListItem
                key={index}
                sx={{
                  p: "10px",
                  cursor: "pointer",

                  "& .MuiListItemText-primary": {
                    fontSize: "12px !important",
                    color: "#000 !important",
                  },
                }}
                onClick={() => {
                  setSearchResults([]);

                  dispatch(
                    bulkUpdateCheckoutStore({
                      type: "",
                      value: {
                        userLocation: { lat: result.y, lng: result.x },
                      },
                    })
                  );

                  getAddress({ lat: result.y, lng: result.x });
                }}
              >
                <ListItemText primary={result.label} />
              </ListItem>
            ))}
          </List>
        </ClickAwayListener>
      )}
    </Grid>
  );
};

export default LeafletProvider;
