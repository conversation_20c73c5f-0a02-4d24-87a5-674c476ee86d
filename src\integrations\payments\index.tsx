import BankAlfalah from "./bankAlfalah";
import PayMob from "./payMob";
import PayFast from "./payFast";

import { paymentMethods } from "utils/constant";
import { useSelector } from "store";

const Payments: React.FC = () => {
  const { paymentType } = useSelector((state) => state.checkout);

  // ==============================|| UI ||============================== //

  // function to initialize the respective payment method (don't have UI)
  const getPaymentMethod = (): JSX.Element => {
    switch (paymentType?.type?.toLowerCase()) {
      case paymentMethods?.BANK_ALFALAH?.value:
        return <BankAlfalah />;
      case paymentMethods?.PAY_MOB?.value:
        return <PayMob />;
      case paymentMethods?.PAY_FAST?.value:
        return <PayFast />;
      default:
        return <></>;
    }
  };

  return <>{getPaymentMethod()}</>;
};

export default Payments;
