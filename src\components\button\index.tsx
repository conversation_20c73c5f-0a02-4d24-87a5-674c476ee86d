import { CircularProgress, <PERSON><PERSON> as M<PERSON><PERSON><PERSON><PERSON> } from "@mui/material";

import { buttonStyles, buttonLoaderStyles } from "components/index.styles";
import { ButtonProps } from "components/index.types";
import { useSelector } from "store";

const Button = ({
  loading,
  children,
  variant,
  sx,
  color,
  size,
  disabled,
  onClick,
  ref,
  ...rest
}: ButtonProps) => {
  const { theme } = useSelector((state) => state.checkout);

  return (
    <MUIButton
      variant={variant || "contained"}
      color={color}
      onClick={onClick}
      sx={{
        textTransform: "unset",
        backgroundColor: theme?.buttonBackground || "#000",
        color: theme?.buttonFontColor || "#fff",
        borderRadius:
          (theme?.buttonRadius && `${theme?.buttonRadius}px`) || "6px",

        "&:hover": {
          backgroundColor: theme?.buttonBackground || "#000",
        },

        ...(disabled && {
          backgroundColor:
            theme?.buttonBackground || "rgba(0, 0, 0, 0.12) !important",
          color: theme?.buttonBackground || "rgba(0, 0, 0, 0.26) !important",
        }),

        ...buttonStyles,
        ...sx,
      }}
      size={size}
      disabled={disabled}
      ref={ref}
      {...rest}
    >
      {loading ? (
        <>
          <CircularProgress size={24} sx={buttonLoaderStyles} />
          <span style={{ opacity: 0 }}>{children}</span>
        </>
      ) : (
        children
      )}
    </MUIButton>
  );
};

export default Button;
