import { useCallback } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { ErrorResponse } from "types";
import { login, setUserAuthResponse } from "utils/apiConfig";
import { userNotification } from "utils/helperFunctions";
import { useSelector } from "store";

export const useLogin = () => {
  const { businessId, paymentDetails } = useSelector((state) => state.checkout);

  // ==============================|| API ||============================== //

  // API call to login user
  const [{ data, loading, error }, loginAPICall] = useAxios(login(businessId), {
    manual: true,
  });

  // ==============================|| handler functions ||============================== //

  // function to call login API
  const callLoginAPI = useCallback(
    async (config: AxiosRequestConfig): Promise<boolean> => {
      try {
        // API call to login user
        const { data } = await loginAPICall(config);

        // verify API response
        if (data?.status === 200 && data.result) {
          // function to map API response and update in store
          setUserAuthResponse(paymentDetails, data.result);

          // function to set message to notify user
          userNotification("Logged in successfully", "success");

          return true;
        }

        return false;
      } catch (error) {
        // handle API status if email or password is wrong
        if (
          (error as ErrorResponse)?.status === 404 &&
          (error as ErrorResponse)?.message?.includes(
            "Invalid email or password"
          )
        ) {
          // function to set message to notify user
          userNotification((error as ErrorResponse)?.message || "");
        } else {
          // function to set message to notify user
          userNotification("Failed to login. Please try again!");
        }

        return false;
      }
    },

    // eslint-disable-next-line react-hooks/exhaustive-deps
    [loginAPICall]
  );

  return {
    data,
    loading,
    error,
    loginAPICall: callLoginAPI,
  };
};
