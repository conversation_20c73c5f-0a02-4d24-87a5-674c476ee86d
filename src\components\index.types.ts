import {
  InputProps,
  SvgIconProps,
  SxProps,
  TextFieldVariants,
} from "@mui/material";
import dayjs from "dayjs";

export interface AccordionProps {
  title: string;
  expanded: boolean;
  content: React.ReactNode;
  expandIcon: React.ReactElement<SvgIconProps>;
  accordionTitleStyles?: SxProps;
  onChange: (event: React.SyntheticEvent, expanded: boolean) => void;
}

export interface SkeletonProps {
  type: "form" | "summary" | "map";
}

export interface ModalProps {
  open: boolean;
  onClose?: () => void;
  children: React.ReactNode;
  sx?: object;
  modalSX?: object;
}

export interface PhoneInputProps {
  label?: string;
  value: string;
  onChange?: (phoneNumber: string) => void;
  labelStyles?: object;
  error?: boolean;
  helperText: string;
  toggleTooltip?: boolean;
}

export interface TextFieldProps {
  id?: string;
  name?: string;
  placeholder?: string;
  variant?: TextFieldVariants;
  label?: string;
  value: string;
  autoFocus?: boolean;
  onKeyPress?: (event?: React.KeyboardEvent<HTMLElement>) => void;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  styles?: object;
  type?: string;
  inputProps?: InputProps;
  error?: boolean;
  helperText?: string;
  disable?: boolean;
  textTransform?: FieldTextTransform;
}

type FieldTextTransform =
  | "none"
  | "capitalize"
  | "uppercase"
  | "lowercase"
  | "full-width"
  | "full-size-kana";

export interface ButtonProps {
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  sx?: Object;
  variant?: "text" | "outlined" | "contained";
  onClick?: (event?: React.MouseEvent<HTMLButtonElement>) => void;
  color?:
    | "inherit"
    | "secondary"
    | "primary"
    | "success"
    | "error"
    | "info"
    | "warning";
  size?: "small" | "medium" | "large";
  startIcon?: React.ReactElement;

  endIcon?: React.ReactElement;
  ref?: React.MutableRefObject<null>;
}

export interface TimePickerProps {
  onChange: (value: dayjs.Dayjs | null) => void;
  disable?: boolean;
}

export interface TooltipProps {
  id?: string;
  open?: boolean;
  message: string;
  children: React.ReactElement;
  disableHoverListener?: boolean;
  disableFocusListener?: boolean;
}
