import { Grid, Typography } from "@mui/material";

import TextField from "components/textField";

import { inputLabelStyle } from "styles";
import { deliveryAddressGrid } from "../index.styles";
import { updateCookies } from "utils/helperFunctions";
import { dispatch, useSelector } from "store";
import { updateCheckoutStore } from "store/slices/checkout";

const SpecialInstructions = () => {
  const { specialInstructions, orderType } = useSelector(
    (state) => state.checkout
  );

  // ==============================|| handler functions ||============================== //

  // function to handle the fields input change
  const handleInputChange = (event: {
    target: { name: string; value: string };
  }): void => {
    const formattedValue: string =
      event.target.value?.length > 0
        ? event.target.value.charAt(0).toUpperCase() +
          event.target.value.slice(1)
        : "";

    // set data in store
    dispatch(
      updateCheckoutStore({ type: event.target.name, value: formattedValue })
    );

    // set data in cookies
    updateCookies({ specialInstructions: formattedValue });
  };

  // ==============================|| UI ||============================== //

  return (
    <Grid container sx={deliveryAddressGrid}>
      <Grid item>
        <Typography component="small" sx={inputLabelStyle}>
          Additional note
        </Typography>

        <TextField
          name="specialInstructions"
          placeholder={`Add ${orderType} instructions`}
          value={specialInstructions}
          onChange={handleInputChange}
        />
      </Grid>
    </Grid>
  );
};

export default SpecialInstructions;
