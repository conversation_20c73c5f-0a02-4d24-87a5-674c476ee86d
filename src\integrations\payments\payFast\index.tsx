import { useEffect } from "react";

import { GenericMap } from "types";
import { paymentMethods } from "utils/constant";
import { convertTimeZoneOffset, getReturnURL } from "utils/helperFunctions";
import { useSelector } from "store";

const PayFast = () => {
  const {
    cartDetails,
    phone,
    email,
    paymentDetails,
    siteUrl,
    checkoutProcessState,
  } = useSelector((state) => state.checkout);
  const { businessDetails, branchDetails } = useSelector(
    (state) => state.business
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // verify payFast token
    if (paymentDetails?.payFast?.token) {
      // function call to create form and submit details to payFast
      submitPayFastForm();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [paymentDetails?.payFast?.token]);

  // ==============================|| handler functions ||============================== //

  // function to create form and submit details to payFast
  const submitPayFastForm = (): void => {
    // create a hidden form to submit details
    const form: HTMLFormElement = document.createElement("form");
    form.method = "POST";
    form.action = `${process.env.REACT_APP_PAYFAST_FORM}`;

    // add form fields
    const fields: GenericMap = {
      MERCHANT_ID: paymentDetails?.payFast?.merchantId,
      MERCHANT_NAME: paymentDetails?.payFast?.merchantName,
      STORE_ID: paymentDetails?.payFast?.storeId,
      TOKEN: paymentDetails?.payFast?.token,
      PROCCODE: "00",
      TXNAMT: cartDetails?.gtotal,
      CURRENCY_CODE: businessDetails?.currencyCode,
      CUSTOMER_MOBILE_NO: phone,
      CUSTOMER_EMAIL_ADDRESS: email,
      TXNDESC: `Inv-${checkoutProcessState?.id}`,
      SUCCESS_URL: getReturnURL(
        siteUrl,
        paymentMethods?.PAY_FAST?.value,
        checkoutProcessState?.id,
        paymentMethods?.PAY_FAST?.id
      ),
      FAILURE_URL: getReturnURL(
        siteUrl,
        paymentMethods?.PAY_FAST?.value,
        checkoutProcessState?.id,
        paymentMethods?.PAY_FAST?.id
      ),
      BASKET_ID: paymentDetails?.payFast?.invoiceId,
      ORDER_DATE: convertTimeZoneOffset(branchDetails?.timeZone),
      CHECKOUT_URL: window.location.href,
    };

    // create hidden input fields for form
    for (const key in fields) {
      const input = document.createElement("input");

      input.type = "hidden";
      input.name = key;
      input.value = fields[key];

      form.appendChild(input);
    }

    // append form to the document body
    document.body.appendChild(form);

    // submit the form
    form.submit();

    // remove the form from the DOM
    document.body.removeChild(form);
  };

  // ==============================|| UI ||============================== //

  return <></>;
};

export default PayFast;
