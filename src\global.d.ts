// variable declaration to get data from website (cross technology)
declare let businessInfo: BusinessInfoType;

// declare a namespace named "Checkout" to access the bank Alfalah configured functions
declare namespace Checkout {
  function configure(config: any): void;
  function showPaymentPage(): void;
}

// declaration for GA4 events, Facebook Pixel & React Native WebView instance to communicate with cross-technology
declare interface Window {
  gtag: (
    command: string,
    eventName: string,
    eventParams?: Record<string, any>
  ) => void;
  ReactNativeWebView?: {
    postMessage: (message: string) => void;
  };
  dataLayer: Array<Record<string, unknown>>;
  fbq: (
    command: string,
    eventName: string,
    eventParams?: Record<string, any>
  ) => void;
}
