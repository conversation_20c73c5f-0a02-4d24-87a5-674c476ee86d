import { useCallback } from "react";

import useAxios from "axios-hooks";

import { ErrorResponse } from "types";
import { getSocialLogin, setUserAuthResponse } from "utils/apiConfig";
import { userNotification } from "utils/helperFunctions";
import { useSelector } from "store";

export const useSocialLogin = () => {
  const { businessId, paymentDetails } = useSelector((state) => state.checkout);

  // ==============================|| API ||============================== //

  // API call to get user's social login details
  const [{ data, loading, error }, socialLoginAPICall] = useAxios(
    {},
    { manual: true }
  );

  // ========================|| handler functions ||======================== //

  // function to call social login API
  const callSocialLoginAPI = useCallback(
    async (
      name: string,
      email: string,
      socialId: string,
      socialPlatform: string
    ): Promise<boolean | ErrorResponse> => {
      try {
        // API call to get user's social login details
        const { data } = await socialLoginAPICall(
          getSocialLogin(businessId, name, email, socialId, socialPlatform)
        );

        // verify API response
        if (data?.status === 200 && data.result) {
          // function to map API response and update in store
          setUserAuthResponse(paymentDetails, data.result);

          // function to set message to notify user
          userNotification("Logged in successfully", "success");

          // return true if user's social details exist
          return true;
        }

        // return false if user's social details does not exist
        return false;
      } catch (error) {
        // function to set message to notify user
        userNotification("Failed to login. Please try again!");

        return error as ErrorResponse;
      }
    },

    // eslint-disable-next-line react-hooks/exhaustive-deps
    [socialLoginAPICall]
  );

  return {
    socialLoginData: data,
    socialLoginLoading: loading,
    socialLoginError: error,
    socialLoginAPICall: callSocialLoginAPI,
  };
};
