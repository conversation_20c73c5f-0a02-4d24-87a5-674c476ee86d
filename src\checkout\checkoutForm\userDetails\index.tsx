import { Box, Grid, Typography } from "@mui/material";

import Tooltip from "components/tooltip";
import TextField from "components/textField";
import PhoneInput from "components/phoneInput";

import { userDetailsGrid } from "checkout/checkoutForm/index.styles";
import { inputLabelStyle } from "styles";
import { phoneErrorMessage, UIElementIds } from "utils/constant";
import {
  capitalizeFirstLetter,
  getLabelColor,
  updateCookies,
} from "utils/helperFunctions";
import { dispatch, useSelector } from "store";
import {
  bulkUpdateCheckoutStore,
  updateCheckoutStore,
} from "store/slices/checkout";

const UserDetails = () => {
  const { name, email, phone, userId, validationError } = useSelector(
    (state) => state.checkout
  );

  // ==============================|| handler functions ||============================== //

  // function to handle the phone number field change
  const handlePhoneInputChange = (phoneNumber: string): void => {
    // set data in store
    dispatch(
      bulkUpdateCheckoutStore({
        type: "",
        value: {
          phone: phoneNumber,
          validationError: { ...validationError, phone: false },
        },
      })
    );

    // set data in cookies
    updateCookies({ phone: phoneNumber });
  };

  // function to handle the fields input change
  const handleInputChange = (event: {
    target: { name: string; value: string };
  }): void => {
    // set data in store
    dispatch(
      updateCheckoutStore({
        type: event.target.name,
        value: event.target.value,
      })
    );

    // reset validation error
    if (validationError[event.target.name]) {
      dispatch(
        updateCheckoutStore({
          type: "validationError",
          value: { ...validationError, [event.target.name]: false },
        })
      );
    }

    // set data in cookies
    updateCookies({
      [event.target.name]:
        event.target.name === "name"
          ? capitalizeFirstLetter(event.target.value)
          : event.target.value,
    });
  };

  // ==============================|| UI ||============================== //

  return (
    <Grid container sx={userDetailsGrid}>
      <Grid item>
        <Typography
          id={UIElementIds?.name}
          component="small"
          sx={{
            ...inputLabelStyle,
            color: getLabelColor(validationError.name),
          }}
        >
          Full name*
        </Typography>

        <TextField
          name="name"
          placeholder="Enter your name"
          value={name}
          onChange={handleInputChange}
          textTransform={"capitalize"}
        />
      </Grid>

      <Grid item>
        <Typography
          id={UIElementIds?.email}
          component="small"
          sx={{
            ...inputLabelStyle,
            color: getLabelColor(validationError.email),
          }}
        >
          Email address*
        </Typography>

        <Tooltip
          message="Email is auto-filled. Log out to update"
          disableHoverListener={!userId}
          disableFocusListener={!userId}
        >
          <Box>
            <TextField
              name="email"
              placeholder="Enter email address"
              value={email}
              disable={!!userId}
              onChange={handleInputChange}
            />
          </Box>
        </Tooltip>
      </Grid>

      <PhoneInput
        label="Phone number*"
        value={phone}
        onChange={handlePhoneInputChange}
        toggleTooltip={validationError.phone}
        labelStyles={{ color: getLabelColor(validationError.phone) }}
        helperText={(validationError.phone && phoneErrorMessage) || ""}
      />
    </Grid>
  );
};

export default UserDetails;
