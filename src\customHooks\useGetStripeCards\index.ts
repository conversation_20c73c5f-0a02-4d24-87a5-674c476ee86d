import { useCallback, useEffect } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { paymentMethods } from "utils/constant";
import { getStripeCardsList } from "utils/apiConfig";
import { setDisableComponentFlag } from "utils/helperFunctions";

import { StripeCardType } from "integrations/payments/index.types";
import { useSelector } from "store";

export const useGetStripeCards = () => {
  const { businessId } = useSelector((state) => state.checkout);

  // ==============================|| API ||============================== //

  // API call to get registered stripe cards of customer
  const [{ data, loading, error }, getStripeCardsAPICall] = useAxios(
    getStripeCardsList(businessId, paymentMethods?.STRIPE?.value, ""),
    { manual: true }
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function to set the API loading state in store to disable components
    setDisableComponentFlag(loading);
  }, [loading]);

  // ==============================|| handler functions ||============================== //

  // function to call get stripe cards list API (customer cards registered on stripe)
  const getStripeCards = useCallback(
    async (config: AxiosRequestConfig): Promise<StripeCardType[]> => {
      try {
        // API call to get registered stripe cards list
        const { data } = await getStripeCardsAPICall(config);

        // verify API response
        if (data?.status === 200 && Array.isArray(data.result)) {
          return data.result;
        }

        return [];
      } catch (error) {
        return [];
      }
    },
    [getStripeCardsAPICall]
  );

  return {
    data,
    loading,
    error,
    getStripeCardsAPICall: getStripeCards,
  };
};
