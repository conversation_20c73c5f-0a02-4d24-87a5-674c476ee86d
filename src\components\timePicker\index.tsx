import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { MobileTimePicker } from "@mui/x-date-pickers/MobileTimePicker";
import { createTheme, ThemeProvider } from "@mui/material/styles";

import { timePickerStyles } from "components/index.styles";
import { TimePickerProps } from "components/index.types";

const theme = createTheme({
  components: {
    MuiTextField: {
      styleOverrides: {
        root: {
          "& .MuiOutlinedInput-root": {
            "& fieldset": {
              borderColor: "#E4E4E4",
            },
            "&:hover fieldset": {
              borderColor: "#E4E4E4",
            },
            "&.Mui-focused fieldset": {
              borderColor: "#E4E4E4",
            },
          },
          "& .MuiInputLabel-root": {
            color: "#BABABA",
            fontSize: "14px",
            fontWeight: 400,
          },
          "& .MuiInputLabel-root.Mui-focused": {
            color: "#BABABA",
          },
          "& .MuiInputBase-input": {
            color: "black",
            fontSize: "14px",
            fontWeight: 400,
          },
        },
      },
    },
    MuiFormControl: {
      styleOverrides: {
        root: {
          "& .MuiFormLabel-root": {
            color: "black",
          },
          "& .MuiFormLabel-root.Mui-focused": {
            color: "black",
          },
          "& .MuiInputBase-input": {
            color: "black",
          },
        },
      },
    },
  },
});

const TimePicker = ({ onChange, disable }: TimePickerProps) => {
  const value = null;

  return (
    <ThemeProvider theme={theme}>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <MobileTimePicker
          value={value}
          onChange={onChange}
          disabled={disable}
          sx={timePickerStyles}
        />
      </LocalizationProvider>
    </ThemeProvider>
  );
};

export default TimePicker;
