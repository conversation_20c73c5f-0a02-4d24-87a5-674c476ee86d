import { useState } from "react";

import {
  Divider,
  Paper,
  Stack,
  Typography,
  Box,
  SxProps,
  Theme,
} from "@mui/material";
import Item from "@mui/material/Stack";
import { ExpandMore } from "@mui/icons-material";

import Accordion from "components/accordion";
import Coupon from "../coupon";
import {
  productsDetails,
  productsAccordionTitle,
  calculationText,
  summarySubTotalPrice,
  summarySubTotal,
  calculationTotal,
} from "./index.styles";
import { dividerStyle, paperStyles } from "../index.styles";
import { blackColor, greenColor, justifySpaceBetween } from "styles";
import { CartItemsType } from "types";
import { getFormattedPrice, orderItemsCount } from "utils/helperFunctions";
import { useSelector } from "store";

const CartSummary = () => {
  const [expandedAccordion, setExpandedAccordion] = useState<string>("");

  const { cartDetails } = useSelector((state) => state.checkout);
  const { businessDetails } = useSelector((state) => state.business);

  // ==============================|| handler functions ||============================== //

  // function to handle the accordion change
  const handleAccordionChange = (
    expanded: boolean,
    accordionType: string
  ): void => {
    setExpandedAccordion(expanded ? accordionType : "");
  };

  // ==============================|| UI ||============================== //

  // function to display the summary amounts
  const displayFormattedPrice = (
    price: number,
    style: SxProps<Theme> | undefined
  ) => {
    // function to get the formatted price with currency symbol & decimal places
    const formattedPrice: string = getFormattedPrice(
      price,
      businessDetails?.currencyCode,
      businessDetails?.decimalPlaces
    );

    // extract currency symbol and price amount using regex
    const splitPrice: RegExpMatchArray | null =
      formattedPrice?.match(/^(\D+)(.+)$/);

    // if the regex matches, return the price with currency symbol in separate spans (required for tracking)
    if (splitPrice) {
      // destructure the match array to get the currency symbol and price amount
      const [, currencySymbol, priceAmount] = splitPrice;

      // return the price with currency symbol in separate spans
      return (
        <Stack direction="row">
          <Typography component="span" sx={style}>
            {currencySymbol}
          </Typography>

          <Typography component="span" sx={style}>
            {priceAmount.trim()}
          </Typography>
        </Stack>
      );
    }

    // fallback in case the regex doesn't match
    return (
      <Typography component="span" sx={style}>
        {formattedPrice}
      </Typography>
    );
  };

  // function to return UI of cart products in an accordion
  const cartProducts = (): JSX.Element => {
    return (
      <Stack spacing={1.3}>
        {cartDetails?.items?.map((product: CartItemsType) => (
          <Item
            direction="row"
            justifyContent="space-between"
            key={product?.menu_item_id}
          >
            <Typography component="span" sx={productsDetails}>
              {product?.dqty}x {product?.dname}
            </Typography>

            {displayFormattedPrice(product?.dtotal, calculationText)}
          </Item>
        ))}
      </Stack>
    );
  };

  return (
    <Paper elevation={0} sx={paperStyles}>
      <Accordion
        title="Order summary"
        expanded={expandedAccordion === "products"}
        content={cartProducts()}
        expandIcon={<ExpandMore sx={blackColor} />}
        accordionTitleStyles={productsAccordionTitle}
        onChange={(event: React.SyntheticEvent, expanded: boolean) =>
          handleAccordionChange(expanded, "products")
        }
      />

      <Divider sx={dividerStyle} />

      <Stack spacing={2.5}>
        <Typography sx={productsAccordionTitle}>Order total</Typography>

        <Box sx={justifySpaceBetween}>
          <Stack direction="row" spacing={0.75}>
            <Typography sx={calculationText}>Subtotal</Typography>

            <Typography sx={summarySubTotal}>
              {`(${orderItemsCount(cartDetails?.items)} Items)`}
            </Typography>
          </Stack>

          {displayFormattedPrice(cartDetails?.total, summarySubTotalPrice)}
        </Box>
      </Stack>

      <Divider sx={dividerStyle} />

      {/* coupon component */}
      <Coupon />

      <Divider sx={{ borderColor: "#EBEBEB", m: "20px 0px !important" }} />

      <Stack direction={"column"} spacing={1.8}>
        {Number(cartDetails?.discount_value) > 0 && (
          <Item direction="row" justifyContent="space-between">
            <Typography component="span" sx={calculationText}>
              Discount
            </Typography>

            {displayFormattedPrice(
              cartDetails?.discount_value,
              calculationText
            )}
          </Item>
        )}

        {Number(cartDetails?.coupon_discount_value) > 0 && (
          <Item direction="row" justifyContent="space-between">
            <Typography component="span" sx={calculationText}>
              Coupon Discount
            </Typography>

            {displayFormattedPrice(cartDetails?.coupon_discount_value, {
              ...calculationText,
              ...greenColor,
            })}
          </Item>
        )}

        {Number(cartDetails?.tax_value) > 0 && (
          <Item direction="row" justifyContent="space-between">
            <Typography component="span" sx={calculationText}>
              Tax
            </Typography>

            {displayFormattedPrice(cartDetails?.tax_value, calculationText)}
          </Item>
        )}

        {Number(cartDetails?.delivery_charges) > 0 && (
          <Item direction="row" justifyContent="space-between">
            <Typography component="span" sx={calculationText}>
              Delivery Charges
            </Typography>

            {displayFormattedPrice(
              cartDetails?.delivery_charges,
              calculationText
            )}
          </Item>
        )}

        {Number(cartDetails?.tip) > 0 && (
          <Item direction="row" justifyContent="space-between">
            <Typography component="span" sx={calculationText}>
              Tip
            </Typography>

            {displayFormattedPrice(cartDetails?.tip, calculationText)}
          </Item>
        )}

        <Item direction="row" justifyContent="space-between">
          <Typography component="span" sx={calculationTotal}>
            Total
          </Typography>

          {displayFormattedPrice(cartDetails?.gtotal, calculationTotal)}
        </Item>
      </Stack>
    </Paper>
  );
};

export default CartSummary;
