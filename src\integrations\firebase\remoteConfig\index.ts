import { fetchAndActivate, getValue, Value } from "firebase/remote-config";

import { socialLoginDefaultStatus } from "utils/constant";
import { captureErrorWithSentry } from "utils/helperFunctions";
import { remoteConfig } from "integrations/firebase";
import { dispatch } from "store";
import { bulkUpdateFirebaseStore } from "store/slices/firebase";

export const fetchFirebaseRemoteConfig = (): void => {
  // fetch new configs on every initialization
  remoteConfig.settings.minimumFetchIntervalMillis = 0;

  // fetch and set configurations from Firebase remote configurations
  fetchAndActivate(remoteConfig)
    .then(() => {
      // get value from Firebase using remoteConfig key
      const firebaseSettings: Value = getValue(
        remoteConfig,
        `firebaseSettings`
      );

      // return if failed to fetch configurations
      if (!firebaseSettings) {
        return;
      }

      // parsing the Firebase returned object to make it usable
      const parsedSettings = JSON.parse(JSON.stringify(firebaseSettings));

      // return if failed to get value
      if (!parsedSettings?._value) {
        return;
      }

      // parsing the stringified value
      const configurationDetails = JSON.parse(parsedSettings?._value);

      // destructure configuration based on environment
      const { socialLogin } =
        configurationDetails?.[process.env.REACT_APP_NODE_ENV!];

      // update configurations in store
      dispatch(
        bulkUpdateFirebaseStore({
          type: "",
          value: {
            socialLogin: socialLogin || socialLoginDefaultStatus,
            settings: {
              expireLogs: configurationDetails?.settings?.expireLogs || 7,
            },
          },
        })
      );
    })
    .catch((error) => {
      // function call to log the exception to Sentry
      captureErrorWithSentry(error);
    });
};
