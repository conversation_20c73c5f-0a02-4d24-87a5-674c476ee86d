import { useRef } from "react";

import { Stack } from "@mui/material";
import { CardNumberElement } from "@stripe/react-stripe-js";
import { StripeCardNumberElement } from "@stripe/stripe-js";

import { useCreateOrder } from "customHooks/useCreateOrder";
import { usePayMobPayment } from "customHooks/usePayMobPayment";
import { useAlfalahPayment } from "customHooks/useAlfalahPayment";
import { usePayFastPayment } from "customHooks/usePayFastPayment";
import { useStripePayment } from "customHooks/useStripePayment";
import { useSubscription } from "customHooks/useSubscription";
import { useDestroyPHPSession } from "customHooks/useDestroyPHPSession";
import { useRenewCustomerAuthToken } from "customHooks/useRenewCustomerAuthToken";
import { useRegisterStripeCustomer } from "customHooks/useRegisterStripeCustomer";

import Button from "components/button";
import { formButtonParent } from "../index.styles";
import { StripeCustomerInfo, SubmitFormProps } from "checkout/index.types";
import {
  orderValidation,
  cashPaymentMessage,
  clearStorage,
  paymentFailureMessage,
  paymentSuccessMessage,
  userNotification,
  getAlfalahReturnURL,
  pushGA4Events,
  pushPixelEvents,
  handleCustomConfirmationPage,
  subscriptionMessage,
} from "utils/helperFunctions";
import {
  GA4Events,
  PixelEvents,
  paymentMethods,
  sourceTypes,
} from "utils/constant";
import {
  createOrderAPIPayload,
  subscriptionAPIPayload,
  paymentChargeAPIPayload,
  registerStripeCustomerAPIPayload,
} from "utils/apiConfig";

import { elements, stripe } from "integrations/payments/stripe/stripeForm";
import { dispatch, useSelector } from "store";
import { updateCheckoutStore } from "store/slices/checkout";

const SubmitForm = ({ styles }: SubmitFormProps) => {
  let processDetails = useRef({
    id: 0,
    token: "",
    customerId: "",
    cardId: "",
    authToken: "",
  });

  const checkoutDetails = useSelector((state) => state.checkout);
  const { businessDetails, branchDetails } = useSelector(
    (state) => state.business
  );

  const {
    siteUrl,
    businessId,
    branchId,
    source,
    email,
    cartDetails,
    paymentType,
    couponCode,
    disableComponentFlag,
    confirmationRedirectURL,
  } = checkoutDetails;

  // ==============================|| custom hooks ||============================== //

  // custom hook to call get payMob payment token API
  const { loading: getPayMobTokenLoading, getPayMobTokenAPICall } =
    usePayMobPayment();

  // custom hook to get bank alfalah payment session
  const { loading: getAlfalahSessionLoading, getAlfalahSessionAPICall } =
    useAlfalahPayment();

  // custom hook to call get payFast payment token API
  const { loading: getPayFastTokenLoading, getPayFastTokenAPICall } =
    usePayFastPayment();

  // custom hook to call stripe payment charge API
  const { loading: stripePaymentLoading, stripePaymentChargeAPICall } =
    useStripePayment();

  // custom hook to call subscription API
  const { loading: subscriptionLoading, subscriptionAPICall } =
    useSubscription();

  // custom hook to call create order API
  const {
    loading: createOrderLoading,
    error: createOrderError,
    createOrderAPICall,
  } = useCreateOrder();

  // custom hook to call php session destroy API (checkout from obw)
  const {
    loading: destroySessionLoading,
    error: destroySessionError,
    destroySessionAPICall,
  } = useDestroyPHPSession();

  // custom hook to call renew customer auth token API
  const {
    loading: renewCustomerAuthTokenLoading,
    renewCustomerAuthTokenAPICall,
  } = useRenewCustomerAuthToken();

  // custom hook to call verify stripe payment customer API
  const {
    loading: registerStripeCustomerLoading,
    registerStripeCustomerAPICall,
  } = useRegisterStripeCustomer();

  // ==============================|| handler functions ||============================== //

  if (createOrderError || destroySessionError) {
    // set data in store to reflect errors
    dispatch(updateCheckoutStore({ type: "apiCallError", value: true }));
  }

  // function to handle the checkout form submission process
  const handleFormSubmission = async (): Promise<void> => {
    // validate order details before submission
    if (!orderValidation(checkoutDetails, businessDetails, branchDetails)) {
      return;
    }

    // create stripe token against card if enabled
    const tokenFlag: boolean = await createStripeToken();

    // stop process if failed to get the success response
    if (!tokenFlag) {
      return;
    }

    /**
     * if => call subscription API if subscription enabled
     * else => call create order API
     */
    if (checkoutDetails?.subscription?.type) {
      // function to handle the subscription flow
      handleSubscriptionProcess();
    } else {
      // function to handle the create order flow
      handleCreateOrderProcess();
    }
  };

  // function to create a stripe token against entered card
  const createStripeToken = async (): Promise<boolean> => {
    /**
     * return true if selected payment method is not stripe
     * or if any of the stripe saved card is selected
     */
    if (
      paymentType.type?.toLowerCase() !== paymentMethods?.STRIPE?.value ||
      checkoutDetails?.paymentDetails?.stripe?.selectedCardId
    ) {
      return true;
    }

    // variable to store error message about card validation from stripe
    let errorMessage: string = "";

    // verify stripe SDK and card elements
    if (stripe && elements) {
      // get card details (card number, expiry, cvc)
      const cardElement: StripeCardNumberElement | null =
        elements.getElement(CardNumberElement);

      // create stipe token
      const tokenResponse = await stripe.createToken(cardElement!);

      // verify response
      if (tokenResponse?.error?.message) {
        // set error message in variable
        errorMessage = tokenResponse?.error?.message || "";
      } else if (tokenResponse?.token?.id) {
        // set token in state
        processDetails.current.token = tokenResponse?.token?.id;

        return true;
      }
    }

    // function to set message to notify user
    userNotification(
      errorMessage || "Failed to connect with payment method. Please try again!"
    );

    return false;
  };

  // function to handle the subscription flow
  const handleSubscriptionProcess = async (): Promise<void> => {
    // verify if stripe payment method is selected
    if (paymentType.type?.toLowerCase() === paymentMethods?.STRIPE?.value) {
      // API call to renew customer auth token
      processDetails.current.authToken = await renewCustomerAuthTokenAPICall();

      // verify user auth token
      if (!processDetails.current.authToken) {
        // function to set message to notify user
        userNotification("Failed to verify user. Please try again!");

        return;
      }

      /**
       * verifying if user has not selected any saved card => user is adding new card
       * note: register customer API creates a new stripe customer or add new card for existing customer
       */
      if (!checkoutDetails?.paymentDetails?.stripe?.selectedCardId) {
        // function to call stripe customer registration API
        await callRegisterStripeCustomerAPI();

        // verify customer registration on stripe
        if (
          !processDetails?.current?.customerId ||
          !processDetails?.current?.cardId
        ) {
          // function to set message to notify user
          userNotification("Failed to register customer. Please try again!");

          return;
        }
      }
    }

    // function to call subscription API
    callSubscriptionAPI();
  };

  // function to call stripe customer registration API
  const callRegisterStripeCustomerAPI = async (): Promise<void> => {
    // API call to register customer (with card) on stripe
    const result: StripeCustomerInfo = await registerStripeCustomerAPICall({
      data: registerStripeCustomerAPIPayload(
        processDetails.current.id,
        businessId,
        branchId,
        email,
        cartDetails.gtotal,
        paymentMethods?.STRIPE?.value,
        processDetails?.current?.token,
        checkoutDetails?.name,
        "",
        checkoutDetails?.userId,
        "",
        checkoutDetails?.paymentDetails?.stripe?.saveCard
      ),
      headers: { Authorization: processDetails?.current?.authToken },
    });

    // set customer details in state
    processDetails.current = {
      ...processDetails?.current,
      customerId: result?.customer_id,
      cardId: result?.card_id,
    };
  };

  // function to call subscription API
  const callSubscriptionAPI = async (): Promise<void> => {
    // subscription API call
    const result: number = await subscriptionAPICall(
      subscriptionAPIPayload(
        checkoutDetails,
        branchDetails,

        processDetails?.current?.customerId ||
          checkoutDetails?.paymentDetails?.stripe?.stripeCustomerId,
        processDetails?.current?.cardId ||
          checkoutDetails?.paymentDetails?.stripe?.selectedCardId
      )
    );

    // show confirmation message & clear storage
    if (result) {
      processDetails.current.id = result;

      // function to clear localStorage & destroy php session (in case of OBW)
      await handleOrderConfirmation();

      /**
       * if => redirect to custom page if url exists
       * else => display the application's confirmation page
       */
      if (confirmationRedirectURL) {
        // function to save data and redirect to the custom page
        handleCustomConfirmationPage(
          confirmationRedirectURL,
          processDetails.current.id,
          checkoutDetails
        );
      } else {
        // function to set messages for confirmation page
        subscriptionMessage(processDetails.current.id);
      }
    }
  };

  // function to handle the create order flow
  const handleCreateOrderProcess = (): void => {
    // TODO: guest checkout here

    // function to call create order API
    callCreateOrderAPI();
  };

  // function to call create order API
  const callCreateOrderAPI = async (): Promise<void> => {
    // create order API call
    const result: number = await createOrderAPICall(
      createOrderAPIPayload(checkoutDetails, branchDetails)
    );

    // verify API response for order existence
    if (result) {
      processDetails.current.id = result;

      // GA4 | function call to log GA4 events (event => add payment info)
      pushGA4Events(
        GA4Events?.ADD_PAYMENT_INFO,
        businessDetails?.currencyCode,
        cartDetails,
        result,
        couponCode
      );

      // GA4 | function call to log GA4 events (event => purchase)
      pushGA4Events(
        GA4Events?.PURCHASE,
        businessDetails?.currencyCode,
        cartDetails,
        result,
        couponCode
      );

      // Pixel | function call to log pixel events (event => purchase)
      pushPixelEvents(
        PixelEvents.PURCHASE,
        businessDetails?.currencyCode,
        cartDetails,
        result,
        couponCode
      );

      // function to initiate respective payment method
      verifyPaymentMethod();
    }
  };

  // call respective payment method
  const verifyPaymentMethod = async (): Promise<void> => {
    switch (paymentType.type?.toLowerCase()) {
      case paymentMethods?.STRIPE?.value: {
        stripePaymentMethod();

        break;
      }
      case paymentMethods?.BANK_ALFALAH?.value: {
        alfalahPaymentMethod();

        break;
      }
      case paymentMethods?.PAY_MOB?.value: {
        payMobPaymentMethod();

        break;
      }
      case paymentMethods?.PAY_FAST?.value: {
        payFastPaymentMethod();

        break;
      }
      default: {
        // cash payment case
        // function to handle process after order placement
        await handleOrderConfirmation();

        // function to set messages for confirmation page
        cashPaymentMessage(processDetails?.current.id);

        break;
      }
    }
  };

  // function to initiate stripe payment API call
  const stripePaymentMethod = async (): Promise<void> => {
    let stripeCustomerId: string = "";
    let stripeCardId: string = "";

    if (checkoutDetails?.paymentDetails?.stripe?.selectedCardId) {
      // API call to renew customer auth token
      processDetails.current.authToken = await renewCustomerAuthTokenAPICall();

      // verify user auth token
      if (!processDetails.current.authToken) {
        // function to set message to notify user
        userNotification("Failed to verify user. Please try again!");

        return;
      }

      stripeCustomerId =
        checkoutDetails?.paymentDetails?.stripe?.stripeCustomerId;
      stripeCardId = checkoutDetails?.paymentDetails?.stripe?.selectedCardId;
    } else if (checkoutDetails?.paymentDetails?.stripe?.saveCard) {
      // API call to renew customer auth token
      processDetails.current.authToken = await renewCustomerAuthTokenAPICall();

      // verify user auth token
      if (!processDetails.current.authToken) {
        // function to set message to notify user
        userNotification("Failed to verify user. Please try again!");

        return;
      }

      /**
       * verifying if user has not selected any saved card => user is adding new card
       * note: register customer API creates a new stripe customer or add new card for existing customer
       */
      if (!checkoutDetails?.paymentDetails?.stripe?.selectedCardId) {
        // function to call stripe customer registration API
        await callRegisterStripeCustomerAPI();

        stripeCustomerId = processDetails?.current?.customerId;
        stripeCardId = processDetails?.current?.cardId;
      }
    }

    // API call to charge payment
    const paymentCharged: boolean = await stripePaymentChargeAPICall({
      ...paymentChargeAPIPayload(
        processDetails.current.id,
        businessId,
        branchId,
        email,
        cartDetails.gtotal,
        businessDetails?.currencyCode,
        paymentMethods?.STRIPE?.value,
        stripeCardId || processDetails?.current?.token,
        "",
        "",
        stripeCustomerId
      ),

      headers: { Authorization: processDetails?.current?.authToken },
    });

    // function to clear localStorage & destroy php session (in case of OBW)
    await handleOrderConfirmation();

    // toggle payment status success/failure message
    if (paymentCharged) {
      if (confirmationRedirectURL) {
        // function to save data and redirect to the custom page
        handleCustomConfirmationPage(
          confirmationRedirectURL,
          processDetails.current.id,
          checkoutDetails
        );
      } else {
        paymentSuccessMessage(processDetails.current.id);
      }
    } else {
      paymentFailureMessage(processDetails.current.id);
    }
  };

  // function to initiate bank alfalah payment API call
  const alfalahPaymentMethod = async (): Promise<void> => {
    // API call to get alfalah session
    await getAlfalahSessionAPICall(
      paymentChargeAPIPayload(
        processDetails.current.id,
        businessId,
        branchId,
        email,
        cartDetails.gtotal,
        businessDetails?.currencyCode,
        paymentMethods?.BANK_ALFALAH?.value,
        "",
        getAlfalahReturnURL(
          siteUrl,
          processDetails.current.id,
          paymentMethods?.BANK_ALFALAH?.id
        ),
        "",
        ""
      ),
      processDetails.current.id
    );
  };

  // function to initiate payMob payment API call
  const payMobPaymentMethod = async (): Promise<void> => {
    // API call to get payMob token
    await getPayMobTokenAPICall(
      paymentChargeAPIPayload(
        processDetails.current.id,
        businessId,
        branchId,
        email,
        cartDetails.gtotal,
        businessDetails?.currencyCode,
        paymentMethods?.PAY_MOB?.value,
        "",
        "",
        "",
        ""
      ),
      processDetails.current.id
    );
  };

  // function to initiate payFast payment API call
  const payFastPaymentMethod = async (): Promise<void> => {
    // API call to get payFast payment token
    await getPayFastTokenAPICall(
      paymentChargeAPIPayload(
        processDetails.current.id,
        businessId,
        branchId,
        email,
        cartDetails.gtotal,
        businessDetails?.currencyCode,
        paymentMethods?.PAY_FAST?.value,
        "",
        "",
        "",
        ""
      ),
      processDetails.current.id
    );
  };

  // function to handle process after order placement
  const handleOrderConfirmation = async (): Promise<void> => {
    // function to clear the cart from localStorage in case of web (ORDRZ & OBW)
    if (source !== sourceTypes?.APP) {
      clearStorage();
    }

    // API call to destroy php session (checkout from old obw)
    if (source === sourceTypes?.WEB_OBW) {
      await destroySession();
    }
  };

  // function call to destroy php session API (checkout from old obw)
  const destroySession = async (): Promise<void> => {
    try {
      await destroySessionAPICall();
    } catch (error) {
      dispatch(updateCheckoutStore({ type: "apiCallError", value: true }));
    }
  };

  // ==============================|| UI ||============================== //

  return (
    <Stack spacing={1.25} sx={{ ...formButtonParent, ...styles }}>
      <Button
        loading={
          createOrderLoading ||
          subscriptionLoading ||
          stripePaymentLoading ||
          getAlfalahSessionLoading ||
          getPayMobTokenLoading ||
          getPayFastTokenLoading ||
          destroySessionLoading ||
          renewCustomerAuthTokenLoading ||
          registerStripeCustomerLoading
        }
        disabled={
          stripePaymentLoading ||
          getAlfalahSessionLoading ||
          subscriptionLoading ||
          getPayMobTokenLoading ||
          getPayFastTokenLoading ||
          disableComponentFlag ||
          processDetails.current.id
            ? true
            : false
        }
        onClick={handleFormSubmission}
      >
        {checkoutDetails?.subscription?.type
          ? "Subscribe"
          : businessDetails?.checkoutSettings?.submitFormButtonText}
      </Button>
    </Stack>
  );
};

export default SubmitForm;
