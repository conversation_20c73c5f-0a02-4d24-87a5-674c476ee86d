import axios from "axios";
import moment from "moment-timezone";

import { captureErrorWithSentry } from "./helperFunctions";
import { dateTimeFormats } from "./constant";

import { logAPICall } from "integrations/firebase/fireStore";
import { store } from "store";

// create an instance of axios
const axiosServices = axios.create({
  baseURL: process.env.REACT_APP_BASEURL,
});

// request interceptor
axiosServices.interceptors.request.use((config) => {
  // get Redux state
  const state = store?.getState();

  // generate time stamp for document id
  const documentId: string = moment()
    ?.utc()
    ?.format(dateTimeFormats?.DATE_TIME_MILLISECONDS);

  // attach document id to config to utilize in response block
  (config as axios.AxiosRequestConfig & { documentId?: string }).documentId =
    documentId;

  // function call to log the API call in Firebase
  logAPICall(
    {
      businessName: state?.business?.businessDetails?.name,
      businessId: Number(state?.checkout?.businessId) || 0,
      branchId: Number(state?.checkout?.branchId) || 0,
      cartId: state?.checkout?.cartId?.toString() || "",
      url: config.url || "",
      method: config.method || "",
      payload: config.data || {},
    },
    documentId,
    false
  );

  return config;
});

// response interceptor
axiosServices.interceptors.response.use(
  (response) => {
    // function call to log the API call in Firebase
    logAPICall(
      { response: response.data || {}, status: response.status || 0 },
      (response?.config as any)?.documentId,
      true
    );

    return response;
  },
  (error) => {
    // function call to log the API call in Firebase
    logAPICall(
      {
        error: {
          code: error?.code || "",
          message: error?.message || "",
          data: error?.response?.data || {},
        },
        status: error?.status || 0,
      },
      (
        error?.response?.config as axios.AxiosRequestConfig & {
          documentId?: string;
        }
      )?.documentId || "",
      true
    );

    // function call to log the exception to Sentry
    captureErrorWithSentry(error);

    // reject the promise with the error response data or a fallback message
    return Promise.reject(error?.response?.data || "Wrong Services");
  }
);

export default axiosServices;
