import { useEffect } from "react";

import { getPayMobPageURL } from "utils/helperFunctions";
import { useSelector } from "store";

const PayMob = () => {
  const { paymentType, paymentDetails } = useSelector(
    (state) => state.checkout
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // verify payMob token
    if (paymentDetails?.payMobToken) {
      // function call to load payMob payment page
      loadPayMobFrame();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [paymentDetails?.payMobToken]);

  // ==============================|| handler functions ||============================== //

  // function to load payMob payment page
  const loadPayMobFrame = (): void => {
    window.location.replace(
      getPayMobPageURL(paymentType.accountId, paymentDetails?.payMobToken)
    );
  };

  // ==============================|| UI ||============================== //

  return <></>;
};

export default PayMob;
