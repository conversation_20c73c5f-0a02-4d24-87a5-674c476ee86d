export interface AuthValidation {
  name?: boolean;
  phone?: boolean;
  email: boolean;
  password?: boolean;
}

export interface LoginDetails {
  email: string;
  password: string;
  validation?: AuthValidation;
}

export interface SignUpDetails extends LoginDetails {
  name: string;
  phone: string;
  googleId?: string;
  facebookId?: string;
}

export interface ForgotPasswordDetails {
  email: string;
  domain?: string;
}

export interface ManualAuthTypePops {
  toggleUserManualAuth: boolean;
  handleToggleUserManualAuth: () => void;
  authTab: string;
}

export interface UserDetails {
  name: string;
  email: string;
  socialId: string;
}

export interface SocialAuthProps {
  socialPlatform: string;
}
