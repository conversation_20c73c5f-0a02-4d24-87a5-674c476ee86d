import { useCallback, useEffect } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { createOrder } from "utils/apiConfig";
import {
  setDisableComponentFlag,
  userNotification,
} from "utils/helperFunctions";
import { useSelector } from "store";

export const useCreateOrder = () => {
  const { businessId } = useSelector((state) => state.checkout);

  // ==============================|| API ||============================== //

  // API call to post new order
  const [{ data, loading, error }, createOrderAPICall] = useAxios(
    createOrder(businessId),
    { manual: true }
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function to set the API loading state in store to disable components
    setDisableComponentFlag(loading);
  }, [loading]);

  // ==============================|| handler functions ||============================== //

  // function to call create order API
  const callCreateOrderAPI = useCallback(
    async (config: AxiosRequestConfig): Promise<number> => {
      try {
        // API call to post order
        const { data } = await createOrderAPICall(config);

        // verify API response
        if (data?.status === 200 && data.result) {
          return data.result.order_id;
        }

        return 0;
      } catch (error) {
        // function to set message to notify user
        userNotification("Oops! Something went wrong. Please try again");

        return 0;
      }
    },

    [createOrderAPICall]
  );

  return {
    data,
    loading,
    error,
    createOrderAPICall: callCreateOrderAPI,
  };
};
