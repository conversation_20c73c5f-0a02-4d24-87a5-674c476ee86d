import React from "react";

import { CssBaseline, ThemeProvider } from "@mui/material";
import { theme } from "theme";
import { configure } from "axios-hooks";
import { Provider } from "react-redux";

import Snackbar from "components/snackbar";
import Checkout from "./checkout";
import "./styles.css";
import axios from "./utils/axios";

import Payments from "integrations/payments";
import { store } from "store";

configure({ axios });

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <CssBaseline />

        <Checkout />
        <Payments />
        <Snackbar />
      </ThemeProvider>
    </Provider>
  );
};

export default App;
