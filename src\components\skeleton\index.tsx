import { useMemo } from "react";

import { Stack, Skeleton as MUISkeleton, SxProps, Theme } from "@mui/material";

import { SkeletonProps } from "components/index.types";
import { skeletonAnimation } from "utils/constant";
import { getSkeletonColors } from "utils/helperFunctions";
import { useSelector } from "store";

const Skeleton = ({ type }: SkeletonProps) => {
  const checkoutDetails = useSelector((state) => state.checkout);

  // store skeleton color based on light/dark theme
  const skeletonColor: SxProps<Theme> = useMemo(
    () => getSkeletonColors(checkoutDetails?.theme?.bodyBackground),
    [checkoutDetails?.theme?.bodyBackground]
  );

  // ==============================|| UI ||============================== //

  // function to return the respective skeleton with respect to 'type' prop
  const getSkeletonUI = (): JSX.Element => {
    switch (type) {
      case "form": {
        return (
          <Stack spacing={1.5}>
            <MUISkeleton
              animation={skeletonAnimation}
              variant="rounded"
              width="100%"
              height={150}
              sx={skeletonColor}
            />

            <MUISkeleton
              animation={skeletonAnimation}
              variant="rounded"
              width="100%"
              height={200}
              sx={skeletonColor}
            />

            <MUISkeleton
              animation={skeletonAnimation}
              variant="rounded"
              width="100%"
              height={150}
              sx={skeletonColor}
            />
          </Stack>
        );
      }
      case "summary": {
        return (
          <Stack spacing={1.5}>
            <MUISkeleton
              animation={skeletonAnimation}
              variant="rounded"
              width="100%"
              height={450}
              sx={skeletonColor}
            />

            <MUISkeleton
              animation={skeletonAnimation}
              variant="rounded"
              width="100%"
              height={60}
              sx={skeletonColor}
            />
          </Stack>
        );
      }
      default: {
        return (
          <MUISkeleton
            animation={skeletonAnimation}
            variant="rounded"
            width="100%"
            height={400}
          />
        );
      }
    }
  };

  return <>{getSkeletonUI()}</>;
};

export default Skeleton;
