<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Checkout | ORDRZ</title>

    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1" /> -->
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap"
      rel="stylesheet"
    />

    <style>
      * {
        -web-kit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
        padding: 0px;
        margin: 0px;
        font-family: "Plus Jakarta Sans", sans-serif;
      }
      html {
        scroll-behavior: smooth;
      }
      body {
        float: left;
        margin: 0px;
        width: 100%;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->

    <script>
      businessInfo = {
        // // ezeats live
        // cartId: "ee02c4dee2f2823dfa75634abf298faa",
        // businessId: "12536",
        // branchId: "18784",
        // siteUrl: "https://ezeats.tossdown.website",
        // source: "ordrz",
        // orderType: "pickup",
        // // subscriptionType: "weekly",
        // // theme: {
        // //   header_bg: "#000000",
        // //   header_font_color: "#ffffff",
        // //   body_bg: "#000",
        // //   body_font_color: "#ffffff",
        // //   button_bg: "#E33C34",
        // //   button_font_color: "#ffffff",
        // //   button_radius: "50",
        // // },

        // bnb live
        // cartId: "04f320fe1344517cd5caa87c0a652e54",
        // // cartId: "48945879c83a2e4eecfe877c70c80093",
        // // cartId: 'bcd9305bd71c61c6c43e47b7ed9353cf',
        // businessId: "274",
        // branchId: "11888",
        // source: "obw",
        // websiteLink: "https://checkout.ordrz.com/",
        // // subscriptionType: "weekly",

        // // sumo live
        // cartId: "95e42c6daa33b5220f02907b4a9ca7fa",
        // businessId: "10950",
        // branchId: "15735",
        // siteUrl: "https://order.sumo.pk",
        // source: "ordrz",

        // // saiesentiments live
        // cartId: "b9a7e8d2680a6a9e594b2bde3d68f9be",
        // businessId: "13076",
        // branchId: "19945",
        // siteUrl: "https://saiesentiments.ordrz.com",
        // source: "ordrz",

        // // ck-buddy live
        // cartId: "9fd8fb06ab400b898d41189ccb0db4dd",
        // businessId: "12758",
        // branchId: "19158",
        // siteUrl: "https://ckbuddy.com",
        // source: "ordrz",

        // // testingdemo live
        // cartId: "8fb331360fa575de1084fc6dfaef6707",
        // businessId: "12520",
        // branchId: "19343",
        // siteUrl: "https://testingdemo.tossdown.site/",
        // source: "ordrz",
        // orderSchedule: { date: "2025-06-25", time: "3:00 PM" },
        // orderType: "pickup",
        // // subscriptionType: "weekly",

        // // sholay live
        // cartId: "ccb680b6f877b2f58e518d0de5988548",
        // // cartId: "3b40929e92b5c5de85687674633b754a",
        // businessId: "18",
        // branchId: "18",
        // siteUrl: "https://sholay.pk",
        // source: "ordrz",
        // // orderType: "pickup",
        // // subscriptionType: "weekly",

        // sholay staging
        // cartId: "3a777cf2e58400e37627f3cfb9e5ed28",
        // cartId: "7271f2f489ec4aeaa4707021bb9759a6",
        // cartId: "48945879c83a2e4eecfe877c70c80093",
        // cartId: 'bcd9305bd71c61c6c43e47b7ed9353cf',

        // cartId: "c4cf314aa4201e6698c9adf2a0174d30",
        cartId: "6fbc223761d101b0f006b5ef43d260ce",
        businessId: "18",
        branchId: "18",
        // branchId: "18197",
        source: "ordrz",
        websiteLink: "https://checkout.ordrz.com/",
        // subscriptionType: "weekly",
        // theme: {
        //   header_bg: "#000000",
        //   header_font_color: "#ffffff",
        //   body_bg: "#ffffff",
        //   body_font_color: "#ffffff",
        //   button_bg: "#E33C34",
        //   button_font_color: "#ffffff",
        //   button_radius: "50",
        // },

        // // bnb staging
        // // // cartId: "0303131fa50738bb7d5743e00d8c7a09",
        // // cartId: "1a62345613fbcbdcf88c5d2f65063af6",
        // // cartId: "89821a11f2b56b54db8fa037a530cee9",
        // cartId: "ae9f3f68e07ad9f6e7631bc81fd619eb",
        // businessId: "274",
        // // branchId: "17860",
        // branchId: "18191",
        // // siteUrl: window.location.href,
        // siteUrl: "https://breadbeyond.staging.tossdown.site",
        // source: "obw",

        // // jalal sons staging
        // // cartId: "7379af9f1f8c63238ba1f47231012a66",
        // cartId: "46fef44c2e7b9d483e40a12f3215b749",
        // businessId: "1130",
        // branchId: "16642",
        // siteUrl: "https://jalalsons.staging.tossdown.site",
        // source: "obw",

        // bolan pass staging
        // cartId: "59a3f4c175d96764976f7d4a2ac3851a",
        // businessId: "12237",
        // branchId: "18131",
        // siteUrl: "https://bolanpass.staging.tossdown.site",
        // source: "obw",

        // // tezmart staging
        // cartId: "6a664eb3f64589e2ed4c9b7cf0595b23",
        // businessId: "12154",
        // branchId: "17948",
        // // branchId: "16282",
        // siteUrl: "https://tezmart.staging.tossdown.site",
        // source: "obw",

        // // ezeats staging
        // // cartId: "abc09459d8f18c8f995c3944c3186b81",
        // // cartId: "c9b960f5-7711-42b8-8ed0-80791b8562db",
        // cartId: "46fc89ab-df63-4807-82cd-c3ef33ced102",
        // businessId: "12483",
        // branchId: "18251",
        // siteUrl: "https://ezeats.staging.tossdown.site",
        // source: "ordrz",
        // orderType: "pickup",
        // // subscriptionType: "weekly",
      };
    </script>
  </body>
</html>
