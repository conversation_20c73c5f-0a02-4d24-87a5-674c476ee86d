import { AlertProps, SnackbarOrigin } from "@mui/material";
import { createSlice } from "@reduxjs/toolkit";

interface SnackbarProps {
  action: boolean;
  open: boolean;
  keepItOpen?: boolean;
  message: string;
  anchorOrigin: SnackbarOrigin;
  alert: AlertProps;
  transition: string;
  close: boolean;
  actionButton: boolean;
}

const initialState: SnackbarProps = {
  action: false,
  open: false,
  keepItOpen: false,
  message: "",
  anchorOrigin: { vertical: "top", horizontal: "center" },
  alert: { color: "error", variant: "filled" },
  transition: "SlideDown",
  close: true,
  actionButton: false,
};

// ==============================|| SLICE - SNACKBAR ||============================== //

const Snackbar = createSlice({
  name: "snackbar",
  initialState,
  reducers: {
    openSnackbar(state, action) {
      const {
        open,
        keepItOpen,
        message,
        anchorOrigin,
        alert,
        transition,
        close,
        actionButton,
      } = action.payload;

      state.action = !state.action;
      state.open = message ? open : initialState.open;
      state.keepItOpen = keepItOpen || initialState.keepItOpen;
      state.message = message;
      state.anchorOrigin = anchorOrigin || initialState.anchorOrigin;
      state.alert = {
        color: alert?.color || initialState.alert.color,
        variant: alert?.variant || initialState.alert.variant,
      };
      state.transition = transition || initialState.transition;
      state.close = close === false ? close : initialState.close;
      state.actionButton = actionButton || initialState.actionButton;
    },

    closeSnackbar(state) {
      state.open = false;
    },
  },
});

export default Snackbar.reducer;

export const { closeSnackbar, openSnackbar } = Snackbar.actions;
