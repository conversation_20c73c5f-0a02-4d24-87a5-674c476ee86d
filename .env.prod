# environment
REACT_APP_NODE_ENV = "production"

# tossdown.com
REACT_APP_BASEURL = "https://tossdown.com/api"

# tossdown.site
REACT_APP_TOSSDOWN_SITE = "https://tossdown.site/api"

# ORDRZ API gateway
REACT_APP_API_GATEWAY = "https://api.tossdown.com"

# cart details
REACT_APP_CART = "https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1"

# business details / create order / stripe
REACT_APP_BUSINESS = "https://d9gwfwdle3.execute-api.us-east-1.amazonaws.com/prod/v1/business"

# stripe public key
REACT_APP_STRIPE_PUBLIC_KEY = "pk_live_51FHCEpEUC8D5duNBQ9AltCsOPkrXB2zlf0dUNtVjTAdSsSNltQdnhhQletxGBOv6qE9Gjo6VhMGzM2XPhNnHJbaE00qjsotEkb"

# bank alfalah script
REACT_APP_ALFALAH_SCRIPT = "https://bankalfalah.gateway.mastercard.com/static/checkout/checkout.min.js"

# payFast payment form submission url
REACT_APP_PAYFAST_FORM = "https://ipg1.apps.net.pk/Ecommerce/api/Transaction/PostTransaction"

# social provider
REACT_APP_SOCIAL_PROVIDER = ""

# firebase app credentials
REACT_APP_FIREBASE_CONFIG = "{"apiKey": "AIzaSyBA4-_OceI7V0alqga49imvhh8m-H5uN5I", "authDomain": "checkout-ea1eb.firebaseapp.com", "projectId": "checkout-ea1eb", "storageBucket": "checkout-ea1eb.firebasestorage.app", "messagingSenderId": "************", "appId": "1:************:web:78d52dabd7f11cf93e6bb5"}"