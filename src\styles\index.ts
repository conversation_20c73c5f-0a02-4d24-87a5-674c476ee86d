export const headerBox = {
  boxShadow: "0px 4px 14px 0px #00000014",
  padding: "6px 10px",
  marginBottom: "25px",
};

export const container = {
  maxWidth: "1200px !important",
  padding: "0px !important",
};

export const formHeading = {
  display: "flex",
  alignItems: "center",
  fontSize: { xs: "16px", sm: "20px" },
  fontWeight: "700",
  lineHeight: "20px",
  marginBottom: "20px",
};

export const resBackIcon = {
  width: "30px",
  height: "30px",
  boxShadow: "0px 4px 14px 0px #00000014",
  borderRadius: "3px",
  alignItems: "center",
  justifyContent: "center",
  marginRight: "10px",
  display: { xs: "flex", sm: "none" },
};

export const checkoutMainBoxParent = {
  margin: "0px",
  padding: "0px 10px",

  "@media (max-width: 1024px)": {
    width: "100%",
    flexDirection: "column",
  },
};

export const checkoutFormBox = {
  padding: "0px",

  "@media (max-width: 1024px)": {
    width: "100%",
    flex: "0 0 100%",
    maxWidth: "100%",
  },
};

export const checkoutSummaryBox = {
  padding: "0px 0px 0px 30px",

  "@media (max-width: 1024px)": {
    padding: "16px 0px 0px 0px",
    width: "100%",
    flex: "0 0 100%",
    maxWidth: "100%",
  },
};

export const inputLabelStyle = {
  fontSize: "14px",
  fontWeight: "500",
  color: "#000",
  paddingBottom: "10px",
  display: "flex",
};

export const inputAutocompleteStyles = {
  borderRadius: "6px",
  p: "0px",

  "& .MuiOutlinedInput-root": {
    padding: "7px 46px 7px 10px",
    fontSize: "12px",
    fontWeight: "400",
    color: "#000",
  },

  "& .MuiOutlinedInput-notchedOutline": {
    border: "1px solid #0000001F",
    borderRadius: "6px",
  },
};

export const inputStyles = {
  outline: "none",
  width: "100%",
  margin: "0px",
  borderRadius: "6px",
  border: "1px solid #0000001F",
  backgroundColor: "#fff",

  "& .MuiInputBase-input": {
    outline: "none",
    border: "none",
    padding: "13px 10px",
    fontSize: "12px",
    fontWeight: "400",
    color: "#000",
  },
  "& .Mui-disabled": { cursor: "not-allowed" },
  "& .MuiInput-underline:before": { borderBottom: "none" },
  "& .MuiInput-underline:after": { borderBottom: "none" },
  "& .MuiInput-underline.Mui-disabled:before": { borderBottom: "none" },
  "& .MuiInput-underline:hover:not(.Mui-disabled):before": {
    borderBottom: "none",
  },
};

export const outlinedInputStyle = {
  width: "100%",

  "& .MuiOutlinedInput-root": {
    "& fieldset": { border: "1px solid #0000001F" },
  },

  "& .MuiOutlinedInput-input": {
    padding: "13px 10px",
    fontSize: "12px",
    fontWeight: "400",
    color: "#000",
  },

  "& label.Mui-focused.Mui-error": {
    color: "#bababa",
    fontWeight: "400",
    fontSize: "16px",
  },
  "& label.Mui-focused": {
    color: "#bababa",
    fontWeight: "400",
    fontSize: "16px",
  },
  "& label.MuiFormLabel-filled": {
    color: "#bababa",
    fontWeight: "400",
    fontSize: "16px",
  },
  "& label.MuiFormLabel-filled.Mui-error": {
    color: "#bababa",
    fontWeight: "400",
    fontSize: "16px",
  },
  "& label.Mui-error": { fontSize: "12px", color: "#bababa", top: "-3px" },
  "& label": { fontSize: "12px", color: "#bababa", top: "-3px" },

  "& .MuiOutlinedInput-root.Mui-focused .MuiOutlinedInput-notchedOutline": {
    border: "1px solid #0000001F",
  },
  "& .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline": {
    border: "1px solid #0000001F",
  },
  "& .MuiOutlinedInput-root:filled .MuiOutlinedInput-notchedOutline": {
    border: "1px solid #0000001F",
  },
  "& .MuiOutlinedInput-root.Mui-error .MuiOutlinedInput-notchedOutline": {
    border: "1px solid #0000001F",
  },
  "& .MuiOutlinedInput-root .MuiOutlinedInput-notchedOutline": {
    border: "1px solid #0000001F",
  },
};

export const selectStyles = {
  outline: "none",
  width: "100%",
  margin: "0px",
  borderRadius: "6px",
  border: "1px solid rgba(0, 0, 0, 0.12)",
  backgroundColor: "#fff",

  "& .css-11u53oe-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input": {
    outline: "unset",
    border: "unset",
    padding: "11.5px 10px",
    fontSize: "14px",
    fontWeight: "400",
    color: "#000",
  },

  "& .css-1d3z3hw-MuiOutlinedInput-notchedOutline": {
    outline: "unset",
    border: "unset",
  },
};

export const tipsAccordionTitle = {
  fontSize: "14px",
  fontWeight: "400",
  color: "#05a357",
};

export const blackColor = { color: "#000" };

export const formDetailTabs = {
  "& .MuiTabs-scroller": { display: "flex", justifyContent: "flex-end" },
};

export const formTabsDesign = {
  "& .MuiTabs-flexContainer": {
    backgroundColor: "#f6f6f6",
    borderRadius: "10px",
    border: "1px solid #E0E0E0",
    background: "#FFF",
    padding: "7px 7px",
    width: { xs: "100%", sm: "unset" },
  },
  "& .Mui-selected": { backgroundColor: "#3B3B3B", color: "#fff !important" },
  "& span": { display: "none" },
};

export const tabsDesign = {
  fontSize: "12.592px",
  fontWeight: "600",
  lineHeight: "17.989px",
  padding: "8.095px 52px",
  borderRadius: "6px",
  textTransform: "capitalize",
  height: "unset",
  minHeight: "unset",
  color: "#000",
  width: { xs: "50%", sm: "unset" },
  "& span": { display: "none" },
};

export const greenColor = { color: "#05a357" };

export const pointer = { cursor: "pointer" };

export const errorPagePaper = {
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  height: "100vh",
  p: "0 15px",
  backgroundColor: "#f6f6f6",
  animation: "fadeIn 0.5s ease-in-out",
};

export const errorCode = {
  fontSize: { xs: "100px", md: "200px" },
  fontWeight: { xs: 700, md: 800 },
  color: "#000000",
};

export const notFoundMessage = {
  fontSize: { xs: "16px", md: "24px" },
  fontWeight: 700,
  color: "#000000",
  mb: "20px",
};

export const errorMessage = {
  fontSize: { xs: "12px", md: "16px" },
  fontWeight: 400,
  textAlign: "center",
  color: "#000000",
  mb: "20px",
};

export const homeButtonStyles = { maxWidth: { xs: "200px", md: "300px" } };

export const stripeFieldContainer = {
  display: "grid",
  gridTemplateColumns: "1fr 1fr",
  gridGap: 10,
};

export const displayRow = {
  display: "flex",
  flexDirection: { xs: "column-reverse", sm: "row" },
  alignItems: "center",
};

export const retryButtonMargin = {
  ml: { sm: "10px" },
  mb: { xs: "10px", sm: "0" },
};

export const errorMessageStyles = {
  color: "#FF0238",
  fontSize: "12px",
  fontWeight: 600,
  letterSpacing: "0.5px",
};

export const stickyContent = {
  position: "sticky",
  background: "#fff",
  p: "20px 28px",
  zIndex: 9,
};

export const relativePosition = { position: "relative" };

export const justifyEnd = {
  display: "flex",
  justifyContent: "flex-end",
  alignItems: "center",
};

export const justifyAlignCenter = {
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
};

export const justifySpaceBetween = {
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
};
