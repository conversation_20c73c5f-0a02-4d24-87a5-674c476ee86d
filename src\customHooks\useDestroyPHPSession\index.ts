import { useCallback, useEffect } from "react";

import useAxios from "axios-hooks";

import { destroyOBWSession } from "utils/apiConfig";
import { setDisableComponentFlag } from "utils/helperFunctions";
import { useSelector } from "store";

export const useDestroyPHPSession = () => {
  const { siteUrl } = useSelector((state) => state.checkout);

  // ==============================|| API ||============================== //

  // API call to destroy php session (checkout from obw)
  const [{ data, loading, error }, destroySessionAPICall] = useAxios(
    destroyOBWSession(siteUrl),
    { manual: true }
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function to set the API loading state in store to disable components
    setDisableComponentFlag(loading);
  }, [loading]);

  // ==============================|| handler functions ||============================== //

  // function to call create order API
  const callPHPSessionDestroyAPI = useCallback(async () => {
    // API call to destroy session (obw)
    await destroySessionAPICall();
  }, [destroySessionAPICall]);

  return {
    data,
    loading,
    error,
    destroySessionAPICall: callPHPSessionDestroyAPI,
  };
};
