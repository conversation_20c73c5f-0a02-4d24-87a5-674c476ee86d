import { useCallback, useEffect } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { subscribeOrderSchedule } from "utils/apiConfig";
import {
  setDisableComponentFlag,
  userNotification,
} from "utils/helperFunctions";

export const useSubscription = () => {
  // ==============================|| API ||============================== //

  // API call to subscribe subscription
  const [{ data, loading, error }, subscriptionAPICall] = useAxios(
    subscribeOrderSchedule(),
    { manual: true }
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function to set the API loading state in store to disable components
    setDisableComponentFlag(loading);
  }, [loading]);

  // ==============================|| handler functions ||============================== //

  // function to call subscription API
  const callSubscriptionAPI = useCallback(
    async (config: AxiosRequestConfig): Promise<number> => {
      try {
        // API call to post subscription
        const { data } = await subscriptionAPICall(config);

        // verify API response
        if (data?.statusCode === 200 && data.scheduleId) {
          return Number(data.scheduleId);
        }

        return 0;
      } catch (error) {
        // function to set message to notify user
        userNotification("Subscription failed. Please try again!");

        return 0;
      }
    },

    [subscriptionAPICall]
  );

  return {
    data,
    loading,
    error,
    subscriptionAPICall: callSubscriptionAPI,
  };
};
