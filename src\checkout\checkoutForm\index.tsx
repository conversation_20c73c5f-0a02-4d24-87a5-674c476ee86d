import React, { useEffect, useState } from "react";

import { Box, Container, Grid, Stack, Typography } from "@mui/material";

import { useGetCart } from "customHooks/useGetCart";
import { useCartCalculation } from "customHooks/useCartCalculation";
import { useGetBusinessDetails } from "customHooks/useGetBusinessDetails";

import Skeleton from "components/skeleton";
import CartSummary from "./cartSummary";
import PaymentDetails from "./paymentDetails";
import OrderTypeDetails from "./orderTypeDetails";
import Subscription from "./subscription";
import SubmitForm from "./submitForm";
import ManageAccount from "checkout/manageAccount";
import ManualAuth from "checkout/manageAccount/manualAuth";

import {
  checkoutMainBoxParent,
  container,
  formHeading,
  checkoutFormBox,
  checkoutSummaryBox,
} from "styles";
import { checkoutBodyBox, formBtnMobile, formBtnWeb } from "./index.styles";
import { CartDetailsType, ErrorResponse } from "types";
import { PaymentOrderDetails } from "checkout/index.types";
import { GA4Events, PixelEvents, userAuthTypes } from "utils/constant";
import { getCartCalculationPayload } from "utils/apiConfig";
import {
  apiFailureMessage,
  pushGA4Events,
  pushPixelEvents,
} from "utils/helperFunctions";

import { fetchFirebaseRemoteConfig } from "integrations/firebase/remoteConfig";
import { dispatch, useSelector } from "store";
import { CheckoutTypes, updateCheckoutStore } from "store/slices/checkout";

const CheckoutForm: React.FC = () => {
  const [toggleUserManualAuth, setToggleUserManualAuth] =
    useState<boolean>(true);

  const checkoutDetails = useSelector((state) => state.checkout);
  const { businessDetails } = useSelector((state) => state.business);

  // ==============================|| custom hooks ||============================== //

  // custom hook to call get business details API
  const { getBusinessDetailsData, getBusinessDetailsAPICall } =
    useGetBusinessDetails();

  // custom hook to call get cart API
  const { getCartData, getCartAPICall } = useGetCart();

  // custom hook to call cart calculation API
  const { getCartCalculationsLoading, getCartCalculationsAPICall } =
    useCartCalculation();

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function call to fetch remote configurations from Firebase
    fetchFirebaseRemoteConfig();

    // function to call business details API
    getBusinessDetails();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // ==============================|| handler functions ||============================== //

  // function to call business details API
  const getBusinessDetails = async (): Promise<void> => {
    // API call to get business details
    const result: PaymentOrderDetails | boolean =
      await getBusinessDetailsAPICall();

    // check if result is of 'PaymentOrderDetails' type
    if (typeof result === "object") {
      // function to call get cart details API
      getCartDetails(result?.orderType, result?.paymentType?.value);
    } else {
      // set message for user on api failure
      apiFailureMessage();
    }
  };

  // function to call get cart details API
  const getCartDetails = async (
    orderType: string,
    paymentType: string
  ): Promise<void> => {
    // API call to get cart details
    const result: CartDetailsType | void = await getCartAPICall();

    // verify API response
    if (result) {
      // maintain store state with cart API response
      const checkoutData: CheckoutTypes = {
        ...checkoutDetails,
        cartDetails: { ...checkoutDetails.cartDetails, ...result },
      };

      // function to call the cart calculation API
      callCartCalculationAPI(checkoutData, paymentType, orderType);

      // GA4 | function call to log GA4 events (event => begin checkout)
      pushGA4Events(
        GA4Events?.BEGIN_CHECKOUT,
        businessDetails?.currencyCode,
        checkoutData?.cartDetails,
        0
      );

      // Pixel | function call to log pixel events (event => begin checkout)
      pushPixelEvents(
        PixelEvents.INITIATE_CHECKOUT,
        businessDetails?.currencyCode,
        checkoutData?.cartDetails,
        0
      );
    }
  };

  // function to handle the cart calculation API
  const callCartCalculationAPI = async (
    checkoutDetails: CheckoutTypes,
    paymentType: string,
    orderType: string
  ): Promise<void> => {
    // API call to calculate cart details
    const result: boolean | ErrorResponse = await getCartCalculationsAPICall(
      getCartCalculationPayload(
        checkoutDetails,
        paymentType,
        orderType,
        checkoutDetails.couponCode,
        checkoutDetails.cartDetails?.tip
      ),
      checkoutDetails?.cartDetails
    );

    // check if result is false OR error object type
    if (!result || typeof result === "object") {
      // set userLocationFlag => true to render error message in case of delivery
      dispatch(updateCheckoutStore({ type: "userLocationFlag", value: true }));
    }
  };

  // function to toggle the modal state
  const handleToggleUserManualAuth = (): void => {
    setToggleUserManualAuth((prevState) => !prevState);
  };

  // ==============================|| UI ||============================== //

  // function to return user auth component based on subscription & user login status
  const getUserAuthComponent = (): JSX.Element => {
    // hide auth component if user is already logged in
    if (checkoutDetails?.userId) {
      return <></>;
    }

    // display auth modal in case of forceUserAuth setting or subscription
    if (
      businessDetails?.checkoutSettings?.forceUserAuth ||
      checkoutDetails?.subscription?.type
    ) {
      // if modal is closed (user continued as guest), show ManageAccount component
      if (!toggleUserManualAuth) {
        return <ManageAccount />;
      }

      // otherwise show the modal
      return (
        <ManualAuth
          toggleUserManualAuth={toggleUserManualAuth}
          handleToggleUserManualAuth={handleToggleUserManualAuth}
          authTab={userAuthTypes?.LOGIN}
        />
      );
    }

    // auth component
    return <ManageAccount />;
  };

  // function to return subscription component
  const getSubscriptionComponent = (): JSX.Element => {
    // return subscription module if subscription type is selected
    if (checkoutDetails?.subscription?.type) {
      return <Subscription />;
    }

    return <></>;
  };

  // function to return payments component
  const getPaymentsComponent = (): JSX.Element => {
    // return payments module if any payment method is available
    if (businessDetails?.paymentMethods?.length) {
      return <PaymentDetails />;
    }

    return <></>;
  };

  return (
    <Box
      sx={{
        ...checkoutBodyBox,
        backgroundColor: checkoutDetails?.theme?.bodyBackground || "#FFF",
      }}
    >
      <Container sx={container}>
        <Typography variant="h5" component="h5" sx={formHeading}></Typography>

        <Grid container sx={checkoutMainBoxParent}>
          <Grid item xs={8} sx={checkoutFormBox}>
            {!getBusinessDetailsData ? (
              <Skeleton type="form" />
            ) : (
              <Stack spacing={2}>
                <>
                  {/* user auth */}
                  {getUserAuthComponent()}

                  {/* delivery/pickup details box */}
                  <OrderTypeDetails />

                  {/* subscription box */}
                  {getSubscriptionComponent()}

                  {/* payment details box */}
                  {getPaymentsComponent()}

                  {/* TODO */}
                  {/* helping information text */}
                  {/* <HelpingText /> */}
                </>
              </Stack>
            )}
          </Grid>

          <Grid item xs={4} sx={checkoutSummaryBox}>
            {!getCartData || getCartCalculationsLoading ? (
              <Skeleton type="summary" />
            ) : (
              <Stack>
                <CartSummary />

                {/* button UI for large screens */}
                <SubmitForm styles={formBtnWeb} />
              </Stack>
            )}
          </Grid>
        </Grid>

        {/* button UI for small screen to make it sticky */}
        {getBusinessDetailsData &&
          getCartData &&
          !getCartCalculationsLoading && <SubmitForm styles={formBtnMobile} />}
      </Container>
    </Box>
  );
};

export default CheckoutForm;
