import { useCallback, useEffect } from "react";

import useAxios from "axios-hooks";

import { renewCustomerAuthToken } from "utils/apiConfig";
import { setDisableComponentFlag } from "utils/helperFunctions";
import { dispatch, useSelector } from "store";
import { updateCheckoutStore } from "store/slices/checkout";

export const useRenewCustomerAuthToken = () => {
  const { businessId, tdUserId, renewAuthToken } = useSelector(
    (state) => state.checkout
  );

  // ==============================|| API ||============================== //

  // API call to renew customer auth token
  const [{ data, loading, error }, renewCustomerAuthTokenAPICall] = useAxios(
    renewCustomerAuthToken(businessId, tdUserId),
    { manual: true }
  );

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function to set the API loading state in store to disable components
    setDisableComponentFlag(loading);
  }, [loading]);

  // ==============================|| handler functions ||============================== //

  // function to call renew customer auth token API
  const getUpdatedCustomerAuthToken = useCallback(async (): Promise<string> => {
    try {
      // API call to renew customer auth token
      const { data } = await renewCustomerAuthTokenAPICall({
        data: { refresh_token: renewAuthToken },
      });

      // verify API response
      if (data?.status === 200 && data?.result?.token) {
        // set user token in store
        dispatch(
          updateCheckoutStore({ type: "authToken", value: data.result?.token })
        );

        return data.result?.token;
      }

      return "";
    } catch (error) {
      return "";
    }
  }, [renewAuthToken, renewCustomerAuthTokenAPICall]);

  return {
    data,
    loading,
    error,
    renewCustomerAuthTokenAPICall: getUpdatedCustomerAuthToken,
  };
};
