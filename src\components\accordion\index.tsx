import {
  Accordion as MUIAccordion,
  AccordionDetails,
  AccordionSummary,
  Typography,
} from "@mui/material";

import {
  accordionDetailsStyles,
  accordionStyles,
  accordionSummaryStyles,
} from "components/index.styles";
import { AccordionProps } from "components/index.types";

const Accordion = ({
  title,
  expanded,
  content,
  expandIcon,
  accordionTitleStyles,
  onChange,
}: AccordionProps) => {
  return (
    <MUIAccordion expanded={expanded} onChange={onChange} sx={accordionStyles}>
      <AccordionSummary expandIcon={expandIcon} sx={accordionSummaryStyles}>
        <Typography sx={accordionTitleStyles}>{title}</Typography>
      </AccordionSummary>

      <AccordionDetails sx={accordionDetailsStyles}>{content}</AccordionDetails>
    </MUIAccordion>
  );
};

export default Accordion;
