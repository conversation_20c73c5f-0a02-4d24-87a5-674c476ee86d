# environment
REACT_APP_NODE_ENV = "development"

# tossdown.com
REACT_APP_BASEURL = "https://beta1.tossdown.com/api"

# tossdown.site
REACT_APP_TOSSDOWN_SITE = "https://staging.tossdown.site/api"

# ORDRZ API gateway
REACT_APP_API_GATEWAY = "https://dev-api.tossdown.com"

# cart details
REACT_APP_CART = "https://qwak95lumk.execute-api.us-east-1.amazonaws.com/dev/v1"

# business details / create order / stripe
REACT_APP_BUSINESS = "https://wn2y9iru2m.execute-api.us-east-1.amazonaws.com/dev/v1/business"

# stripe public key
REACT_APP_STRIPE_PUBLIC_KEY = "pk_test_51Rt2XVI55WYMnZvl3TOJheup6L2c49iybD7gsMQ6HDz58pPrwQh5iUswZhzgqnswsEkuLVXSe7D7ytcuntg2CyHi00RfdV05e2"

# bank alfalah script
REACT_APP_ALFALAH_SCRIPT = "https://test-bankalfalah.gateway.mastercard.com/static/checkout/checkout.min.js"

# payFast payment form submission url
REACT_APP_PAYFAST_FORM = "https://ipguat.apps.net.pk/Ecommerce/api/Transaction/PostTransaction"

# social provider
REACT_APP_SOCIAL_PROVIDER = "https://node-social-login.vercel.app/auth"

# firebase app credentials
REACT_APP_FIREBASE_CONFIG = "{"apiKey": "AIzaSyBA4-_OceI7V0alqga49imvhh8m-H5uN5I", "authDomain": "checkout-ea1eb.firebaseapp.com", "projectId": "checkout-ea1eb", "storageBucket": "checkout-ea1eb.firebasestorage.app", "messagingSenderId": "************", "appId": "1:************:web:78d52dabd7f11cf93e6bb5"}"