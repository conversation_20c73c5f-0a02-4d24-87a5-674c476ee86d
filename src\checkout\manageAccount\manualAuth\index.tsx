import { useState } from "react";

import { <PERSON><PERSON>, Grid, Tab, Ta<PERSON>, Typography, Box } from "@mui/material";
import {
  Close,
  //  TODO: required later
  //  Facebook, Google
} from "@mui/icons-material";

import { useSignUp } from "customHooks/useSignUp";
import { useLogin } from "customHooks/useLogin";
import { useForgotPassword } from "customHooks/useForgotPassword";

import Modal from "components/modal";
import Button from "components/button";
import TextField from "components/textField";
import PhoneInput from "components/phoneInput";

import { outlinedInputStyle, pointer } from "styles";
import {
  authTabs,
  forgotPassword,
  forgotPasswordBox,
  outlinedButtonStyles,
  // TODO: required later
  // authIconStyles,
  // authTypoStyle,
  // facebookButtonStyles,
  // googleButtonStyles,
  // mb10,
} from "../index.styles";
import { modalHeading, modalParent } from "checkout/checkoutForm/index.styles";
import {
  LoginDetails,
  AuthValidation,
  ManualAuthTypePops,
  SignUpDetails,
} from "../index.types";
import { phoneSignupError, userAuthTypes, emailError } from "utils/constant";
import {
  signUpAPIPayload,
  loginAPIPayload,
  forgotPasswordAPIPayload,
} from "utils/apiConfig";
import {
  authValidation,
  getTabValue,
  userNotification,
  validateEmail,
  validatePhoneNumber,
} from "utils/helperFunctions";
import { useSelector } from "store";

const ManualAuth = ({
  toggleUserManualAuth,
  handleToggleUserManualAuth,
  authTab,
}: ManualAuthTypePops) => {
  const [loginDetails, setLoginDetails] = useState<LoginDetails>({
    email: "",
    password: "",
    validation: { email: false, password: false },
  });
  const [signUpDetails, setSignUpDetails] = useState<SignUpDetails>({
    name: "",
    email: "",
    phone: "",
    password: "",
    validation: { name: false, email: false, phone: false, password: false },
  });
  const [resetPasswordDetails, setResetPasswordDetails] = useState({
    email: "",
    validation: { email: false },
  });
  const [selectedAuthType, setSelectedAuthType] = useState<string>(authTab);

  const checkoutDetails = useSelector((state) => state.checkout);
  const { businessDetails } = useSelector((state) => state.business);

  // ==============================|| custom hooks ||============================== //

  // custom hook to call sign up API
  const { signUpLoading, signUpAPICall } = useSignUp();

  // custom hook to call login API
  const { loading: loginLoading, loginAPICall } = useLogin();

  // custom hook to call forgot password API
  const { forgotPasswordLoading, forgotPasswordAPICall } = useForgotPassword();

  // ==============================|| handler functions ||============================== //

  // function to call sign up API
  const callSignUpAPI = async (): Promise<void> => {
    // API call to sign up user
    const result: boolean = await signUpAPICall(
      signUpAPIPayload({
        name: signUpDetails?.name,
        email: signUpDetails?.email,
        phone: signUpDetails?.phone,
        password: signUpDetails?.password,
      })
    );

    // close the modal on successful sign up (getting boolean in result)
    if (result) {
      handleToggleUserManualAuth();
    }
  };

  // function to call login API
  const callLoginAPI = async (): Promise<void> => {
    // API call to login user
    const result: boolean = await loginAPICall(
      loginAPIPayload({
        email: loginDetails?.email,
        password: loginDetails?.password,
      })
    );

    // close the modal on successful sign up (getting boolean in result)
    if (result) {
      handleToggleUserManualAuth();
    }
  };

  // function to call forgot password API
  const callForgotPasswordAPI = async (): Promise<void> => {
    // API call to generate reset password link
    const result: boolean = await forgotPasswordAPICall(
      forgotPasswordAPIPayload({
        email: resetPasswordDetails?.email,
      })
    );

    // update form UI & email on success (getting boolean in result)
    if (result) {
      // display login form
      setSelectedAuthType(userAuthTypes?.LOGIN);

      // set email on login form to prevent rewriting
      setLoginDetails((prevState) => ({
        ...prevState,
        email: resetPasswordDetails?.email,

        // reset any previous validation status
        validation: { email: false, password: false },
      }));
    }
  };

  // function to handle the auth tab change => 0, 1 for login and sign up respectively
  const handleTabChange = (
    event: React.SyntheticEvent,
    tabValue: number
  ): void => {
    setSelectedAuthType(
      tabValue ? userAuthTypes?.SIGN_UP : userAuthTypes?.LOGIN
    );
  };

  // form input fields handler
  const handleInputChange = (event: {
    target: { name: string; value: string };
  }): void => {
    /**
     * if => validate sign up form
     * else if => validate login form
     * else => validate reset password form
     */
    if (selectedAuthType === userAuthTypes?.SIGN_UP) {
      // set sign up form state
      setSignUpDetails((prevState) => ({
        ...prevState,
        [event.target.name]: event.target.value,

        validation: {
          ...signUpDetails?.validation!,

          [event.target.name]: false,
        },
      }));
    } else if (selectedAuthType === userAuthTypes?.LOGIN) {
      // set login form state
      setLoginDetails((prevState) => ({
        ...prevState,
        [event.target.name]: event.target.value,

        validation: {
          ...loginDetails?.validation!,

          [event.target.name]: false,
        },
      }));
    } else {
      // set reset password form state
      setResetPasswordDetails((prevState) => ({
        ...prevState,
        email: event.target.value,

        validation: { email: false },
      }));
    }
  };

  // phone number field input handler
  const handlePhoneInputChange = (phoneNumber: string): void => {
    setSignUpDetails((prevState) => ({
      ...prevState,
      phone: phoneNumber,
      validation: { ...signUpDetails?.validation!, phone: false },
    }));
  };

  // function to submit form on 'Enter' key press
  const handleEnterKeyPress = (
    event: React.KeyboardEvent<HTMLElement> | undefined
  ): void => {
    if (event?.key === "Enter") {
      // function to initiate respective authentication process
      handleAuthentication();
    }
  };

  // function to validate data & call respective authentication API
  const handleAuthentication = (): void => {
    // validate data
    let dataValidation: AuthValidation = { ...loginDetails?.validation! };

    /**
     * if => validate sign up form
     * else if => validate login form
     * else => validate reset password form
     */
    if (selectedAuthType === userAuthTypes?.SIGN_UP) {
      dataValidation = {
        name: !signUpDetails.name?.trim(),
        email: validateEmail(signUpDetails.email?.trim()),
        phone: !validatePhoneNumber(signUpDetails.phone),
        password: !signUpDetails.password,
      };

      // update sign up form state
      setSignUpDetails((prevState) => ({
        ...prevState,
        validation: dataValidation,
      }));
    } else if (selectedAuthType === userAuthTypes?.LOGIN) {
      dataValidation = {
        email: validateEmail(loginDetails?.email?.trim()),
        password: !loginDetails?.password,
      };

      // update login form state
      setLoginDetails((prevState) => ({
        ...prevState,
        validation: dataValidation,
      }));
    } else {
      dataValidation = {
        email: validateEmail(resetPasswordDetails?.email?.trim()),
      };

      // update reset password form state
      setResetPasswordDetails((prevState) => ({
        ...prevState,
        validation: dataValidation,
      }));
    }

    // function to verify if any of the error is true
    const validationStatus: boolean = authValidation(dataValidation);

    // return if any errors exists
    if (validationStatus) {
      return;
    }

    // call respective authentication API
    if (selectedAuthType === userAuthTypes?.SIGN_UP) {
      // function to call sign up API
      callSignUpAPI();
    } else if (selectedAuthType === userAuthTypes?.LOGIN) {
      // function to call login API
      callLoginAPI();
    } else {
      // function to call reset password API
      callForgotPasswordAPI();
    }
  };

  // function to toggle form UI
  const handleForgotPassword = (): void => {
    // set email from login form to reset password form if exists
    if (selectedAuthType === userAuthTypes?.LOGIN && loginDetails?.email) {
      // set reset password form state
      setResetPasswordDetails((prevState) => ({
        ...prevState,
        email: loginDetails?.email,
        validation: { email: loginDetails?.validation?.email! },
      }));
    }

    // toggle form UI
    setSelectedAuthType(
      selectedAuthType === userAuthTypes?.RESET_PASSWORD
        ? userAuthTypes?.LOGIN
        : userAuthTypes?.RESET_PASSWORD
    );
  };

  // function to return respective authentication value to email field
  const getEmailValue = (): string => {
    // check for auth type
    if (selectedAuthType === userAuthTypes?.SIGN_UP) {
      return signUpDetails?.email;
    } else if (selectedAuthType === userAuthTypes?.LOGIN) {
      return loginDetails?.email;
    } else {
      return resetPasswordDetails?.email;
    }
  };

  // function to get error status for email field on respective auth type
  const getEmailErrorStatus = (): boolean => {
    return (
      (selectedAuthType === userAuthTypes?.SIGN_UP &&
        signUpDetails?.validation?.email) ||
      (selectedAuthType === userAuthTypes?.LOGIN &&
        loginDetails?.validation?.email) ||
      (selectedAuthType === userAuthTypes?.RESET_PASSWORD &&
        resetPasswordDetails?.validation?.email)
    );
  };

  // function to get error message for email field
  const getEmailErrorMessage = (): string => {
    // check for auth type
    if (
      selectedAuthType === userAuthTypes?.SIGN_UP &&
      signUpDetails?.validation?.email
    ) {
      return signUpDetails?.email ? emailError : "Required";
    } else if (
      selectedAuthType === userAuthTypes?.LOGIN &&
      loginDetails?.validation?.email
    ) {
      return loginDetails?.email ? emailError : "Required";
    } else if (
      selectedAuthType === userAuthTypes?.RESET_PASSWORD &&
      resetPasswordDetails?.validation?.email
    ) {
      return resetPasswordDetails?.email ? emailError : "Required";
    }

    // no error if email is valid
    return "";
  };

  // function to handle the auth modal closure
  const closeAuthModal = (): void => {
    // prevent modal closure without authentication in case of forceUserAuth setting or subscription
    if (
      businessDetails?.checkoutSettings?.forceUserAuth ||
      checkoutDetails?.subscription?.type
    ) {
      // function to set message to notify user
      userNotification("Please login/sign up to proceed!");

      return;
    }

    // close the modal
    handleToggleUserManualAuth();
  };

  // ==============================|| UI ||============================== //

  // function to return the email field
  const getEmailField = (): JSX.Element => {
    return (
      <Grid item>
        <TextField
          name="email"
          variant="outlined"
          label="Email address"
          value={getEmailValue()}
          autoFocus={!getTabValue(selectedAuthType)}
          onChange={handleInputChange}
          styles={outlinedInputStyle}
          onKeyPress={handleEnterKeyPress}
          error={getEmailErrorStatus()}
          helperText={getEmailErrorMessage()}
        />
      </Grid>
    );
  };

  // function to return the password field
  const getPasswordField = (): JSX.Element => {
    return (
      <Grid item>
        <TextField
          name="password"
          variant="outlined"
          label="Password"
          type="password"
          value={
            getTabValue(selectedAuthType)
              ? signUpDetails?.password
              : loginDetails?.password
          }
          onChange={handleInputChange}
          styles={outlinedInputStyle}
          onKeyPress={handleEnterKeyPress}
          error={
            getTabValue(selectedAuthType)
              ? signUpDetails?.validation?.password
              : loginDetails?.validation?.password
          }
        />
      </Grid>
    );
  };

  // function to return the auth form UI
  const getAuthForm = (): JSX.Element => {
    /**
     * if => validate login/sign up form
     * else => validate reset password form
     */
    if (
      selectedAuthType === userAuthTypes?.SIGN_UP ||
      selectedAuthType === userAuthTypes?.LOGIN
    ) {
      return (
        <>
          {/* auth tabs */}
          <Grid container>
            <Tabs
              variant="fullWidth"
              value={getTabValue(selectedAuthType)}
              onChange={handleTabChange}
              sx={authTabs}
            >
              {Object.values(userAuthTypes)
                ?.slice(0, 2)
                ?.map((auth) => (
                  <Tab key={auth} disableRipple label={auth} />
                ))}
            </Tabs>
          </Grid>

          {/* respective auth type fields */}
          {getTabValue(selectedAuthType) ? (
            <>
              <Grid item>
                <TextField
                  name="name"
                  variant="outlined"
                  label="Full name"
                  value={signUpDetails?.name}
                  textTransform={"capitalize"}
                  autoFocus={true}
                  onChange={handleInputChange}
                  styles={outlinedInputStyle}
                  onKeyPress={handleEnterKeyPress}
                  error={signUpDetails?.validation?.name}
                />
              </Grid>

              {/* email field UI */}
              {getEmailField()}

              {/* phone number component */}
              <PhoneInput
                value={signUpDetails?.phone}
                onChange={handlePhoneInputChange}
                error={signUpDetails?.validation?.phone}
                helperText={phoneSignupError}
              />

              {/* password field UI */}
              {getPasswordField()}
            </>
          ) : (
            <>
              {/* email field UI */}
              {getEmailField()}

              {/* password field UI */}
              {getPasswordField()}
            </>
          )}
        </>
      );
    } else {
      return (
        <>
          {/* email field UI */}
          {getEmailField()}
        </>
      );
    }
  };

  // function to return the auth modal heading
  const authModalHeading = (): string => {
    if (selectedAuthType === userAuthTypes?.SIGN_UP) {
      return "Create new account";
    } else if (selectedAuthType === userAuthTypes?.LOGIN) {
      return "Welcome back!";
    } else {
      return "Forgot password";
    }
  };

  // function to return the auth form text
  const toggleAuthFormText = (): JSX.Element => {
    let formText: string = "";

    // text to display on login form
    if (selectedAuthType === userAuthTypes?.LOGIN) {
      formText = "Forgot Password?";
    } else if (selectedAuthType === userAuthTypes?.RESET_PASSWORD) {
      formText = "Back to Login";
    }

    return (
      (formText && (
        <Box sx={forgotPasswordBox}>
          <Typography sx={forgotPassword} onClick={handleForgotPassword}>
            {formText}
          </Typography>
        </Box>
      )) || <></>
    );
  };

  // function to return the auth form submit button
  const getAuthFormButton = (): JSX.Element => {
    return (
      <Button
        loading={loginLoading || signUpLoading || forgotPasswordLoading}
        disabled={loginLoading || signUpLoading || forgotPasswordLoading}
        onClick={handleAuthentication}
      >
        {(selectedAuthType === userAuthTypes?.SIGN_UP &&
          userAuthTypes?.SIGN_UP) ||
          (selectedAuthType === userAuthTypes?.LOGIN && userAuthTypes?.LOGIN) ||
          (selectedAuthType === userAuthTypes?.RESET_PASSWORD && "Submit")}
      </Button>
    );
  };

  // function to return the guest checkout button
  const getGuestCheckoutButton = (): JSX.Element => {
    // display button only if subscription is not enabled
    if (!checkoutDetails?.subscription?.type) {
      return (
        <Button
          variant="outlined"
          sx={outlinedButtonStyles}
          onClick={handleToggleUserManualAuth}
        >
          Continue as Guest
        </Button>
      );
    }

    return <></>;
  };

  return (
    <Modal open={toggleUserManualAuth}>
      <Stack spacing={2.5} sx={modalParent}>
        <Typography component="strong" sx={modalHeading}>
          {/* modal header message */}
          {authModalHeading()}

          <Close sx={pointer} onClick={closeAuthModal} />
        </Typography>

        {/* auth form UI */}
        {getAuthForm()}

        {/* auth form text UI */}
        {toggleAuthFormText()}

        {/* auth form button UI */}
        {getAuthFormButton()}

        {/* guest checkout button UI */}
        {getGuestCheckoutButton()}

        {/* TODO: required later */}
        {/* <Typography sx={authTypoStyle}>or</Typography> */}

        {/* TODO: required later */}
        {/* social auth buttons */}
        {/* <Stack spacing={1.25}>
          <Button sx={{ ...googleButtonStyles, ...mb10, width: "100%" }}>
            <Google sx={authIconStyles} />
            Continue with Google
          </Button>

          <Button sx={{ ...facebookButtonStyles, width: "100%" }}>
            <Facebook sx={authIconStyles} />
            Continue with Facebook
          </Button>
        </Stack> */}
      </Stack>
    </Modal>
  );
};

export default ManualAuth;
