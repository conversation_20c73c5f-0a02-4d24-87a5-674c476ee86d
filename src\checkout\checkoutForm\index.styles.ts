import { infoMessageStyle } from "./cartSummary/index.styles";
import { buttonLoaderStyles } from "components/index.styles";
import {
  inputStyles,
  justifyAlignCenter,
  justifySpaceBetween,
  stickyContent,
} from "styles";
import { borderColor, borderRadius } from "utils/constant";

export const userDetailsGrid = {
  display: "grid",
  gridTemplateColumns: { xs: "1fr", md: "1fr 1fr 1fr" },
  gridGap: 10,
};

export const paperStyles = {
  padding: { xs: "20px 14px", sm: "20px" },
  backgroundColor: "#FFFFFF",
  border: "1px solid #E4E4E4",
  borderRadius,
  mb: "16px",
};

export const orderTypeRadio = {
  display: "flex",
  flexDirection: "row",
  justifyContent: "flex-start",
  "& label": { marginLeft: "0px !important", marginRight: "28px !important" },
  "& label > span.MuiRadio-root": {
    color: "#000",
    fontSize: "14px",
    fontWeight: 400,
    lineHeight: "20px",
    p: "0px",
    marginRight: "10px",
  },
  "& .MuiTypography-root": { fontSize: "14px" },
};

export const paymentHeadingStyles = {
  fontSize: { xs: "16px", sm: "18px" },
  fontWeight: 700,
  color: "#000",
  paddingBottom: "20px",
  display: "flex",
};

export const paymentRadioParentStyles = {
  gridTemplateColumns: { xs: "1fr", sm: "1fr 1fr" },
  display: "grid",
  gridGap: 10,
};

export const paymentRadioStyles = {
  border: "1px solid",
  padding: "10px",
  margin: "0px",
  borderRadius: "6px",

  "& .MuiButtonBase-root.MuiRadio-root": { padding: "0px" },
  "& span": { fontSize: "14px", fontWeight: 400 },
  "& .MuiTypography-root.MuiTypography-body1.MuiFormControlLabel-label": {
    paddingLeft: "10px",
  },
  "& .css-vqmohf-MuiButtonBase-root-MuiRadio-root.Mui-checked": {
    color: "#141414",
  },

  // class reflect on server
  "& .MuiButtonBase-root.MuiRadio-root.MuiRadio-colorPrimary.Mui-checked": {
    color: "#141414",
  },
};

export const confirmationComponent = {
  backgroundSize: "cover",
  height: "100%",
  width: "100%",
  backgroundImage:
    "url(https://static.tossdown.com/images/9d8bece0-e188-4e3f-b40b-acd9c73bc4af.webp)",
  backgroundRepeat: "no-repeat",
  backgroundPosition: "center",
};

export const confirmationParentBox = {
  display: "flex",
  flexDirection: "column",
  alignItems: "center",
  justifyContent: "center",
  minHeight: "100vh",
  textAlign: "center",
};

export const confirmationChildBox = {
  borderRadius: "16px",
  padding: "20px",
  boxShadow: {
    sm: "0px 3px 3px -2px rgba(0,0,0,0.2), 0px 3px 4px 0px rgba(0,0,0,0.02), 0px 1px 8px 0px rgba(0,0,0,0.05)",
    xs: "unset",
  },
  backgroundColor: "#fff",
  pb: 2,
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  position: "relative",
};

export const cartIconStyles = {
  fontSize: "75px !important",
  color: "#000",
  marginBottom: "30px",
};

export const confirmationId = {
  fontWeight: 800,
  fontSize: "24px",
  color: "#000",
  textAlign: "center",
  pb: "10px",
};

export const orderDetailStyles = {
  ...confirmationId,
  fontWeight: 600,
  fontSize: "16px",
};

export const confirmationOrder = {
  fontWeight: 800,
  fontSize: "18px",
  color: "#000",
  textAlign: "center",
  paddingBottom: "10px",
};

export const confirmationOrderText = {
  lineHeight: "21px",
  fontWeight: 400,
  fontSize: "14px",
  color: "rgba(0, 0, 0, 0.60)",
  textAlign: "center",
  pb: "30px",
};

export const checkoutBodyBox = {
  padding: { xs: "10px 0 0", md: "10px 0px 30px" },
};

export const pickupDatesTabs = {
  "& .MuiTabs-scroller .MuiTabs-flexContainer": {
    justifyContent: "flex-start",
  },
  "& .MuiTabs-scroller .MuiTabs-flexContainer button.toggletabs ": {
    flex: "0 0 33%",
    margin: "0px 10px 0px 0px",
  },
  "& .Mui-selected p": { color: "#000", fontWeight: 600 },
  "& span": { display: "none" },

  textColor: "#000",
};

export const tipsTabs = {
  border: "1px solid rgba(0, 0, 0, 0.12)",
  borderRadius: "6px",

  "& .MuiTabs-scroller .MuiTabs-flexContainer": {
    justifyContent: "flex-start",
  },
  "& .MuiTabs-scroller .MuiTabs-flexContainer button.toggletabs ": {
    flex: { xs: "0 0 33.333%", md: "0 0 25%" },
    m: "0px",
    border: "unset",
    borderRight: "1px solid rgba(0, 0, 0, 0.12)",
    borderRadius: "unset",
  },
  "& .Mui-selected small": { color: "#FFFFFF" },
  "& span": { display: "none" },
};

export const tabContentTypoStyle = {
  color: "rgba(0, 0, 0, 0.50)",
  fontSize: "12px",
  fontWeight: 400,
  lineHeight: "20px",
};

export const tabTypoStyle = {
  fontSize: "12.592px",
  fontWeight: 600,
  lineHeight: "17.989px",
  p: "10px",
  borderRadius: "6px",
  border: "1px solid rgba(0, 0, 0, 0.12)",
  color: "#000",
  textTransform: "unset",
  width: { xs: "50%", sm: "unset" },
};

export const pickupTabTypoStyle = {
  ...tabTypoStyle,

  "& p": tabContentTypoStyle,
};

export const tipTabTypoStyle = {
  ...tabTypoStyle,

  "& small": {
    color: "#000000",
    fontSize: "12px",
    fontWeight: 400,
    lineHeight: "20px",
  },
};

export const timeSlotStyles = {
  "& .MuiFormControlLabel-root": {
    justifyContent: "space-between",
    ml: 0,
    p: "10px 0",
    borderBottom: `1px solid ${borderColor}`,

    ":first-of-type": { pt: "0px" },
    ":last-child": { borderBottom: "none", pb: "0px" },
  },

  "& label span": {
    color: "#000",
    fontSize: "14px",
    fontWeight: 400,
    lineHeight: "20px",
  },
  "& label .Mui-checked span": { fontWeight: 600 },
};

export const tabsScrollStyles = {
  width: "100%",
  position: "relative",
  pl: "0px",

  "& .MuiButtonBase-root.MuiTabScrollButton-root.MuiTabScrollButton-horizontal.MuiTabs-scrollButtons":
    {
      position: "absolute",
      background: "#D9D9D9",
      width: "18px",
      height: "18px",
      borderRadius: "50%",
      zIndex: 1,
    },

  "& .MuiTabs-root > div.MuiButtonBase-root.MuiTabScrollButton-root.MuiTabScrollButton-horizontal.MuiTabs-scrollButtons:nth-of-type(1)":
    { left: "0px", top: "20px" },
  "& .MuiTabs-root > div.MuiButtonBase-root.MuiTabScrollButton-root.MuiTabScrollButton-horizontal.MuiTabs-scrollButtons:last-of-type":
    { right: "0px", top: "20px" },

  // class reflect on server
  "& .MuiTabs-root.css-mrsows > div.MuiButtonBase-root.MuiTabScrollButton-root.MuiTabScrollButton-horizontal.MuiTabs-scrollButtons:nth-of-type(1)":
    { left: "0px", top: "20px" },
  "& .MuiTabs-root.css-mrsows > div.MuiButtonBase-root.MuiTabScrollButton-root.MuiTabScrollButton-horizontal.MuiTabs-scrollButtons:last-of-type":
    { right: "0px", top: "20px" },
};

export const deliveryParentGrid = {
  gridTemplateColumns: "1fr",
  height: "100%",
  display: "grid",
  background: "#fff",
};

export const deliveryDetailsGrid = {
  gridTemplateColumns: { xs: "1fr" },
  display: "grid",
  gridGap: 10,
  padding: "0px 20px 0px 20px",
  border: "unset",
};

export const modalHeading = {
  display: "flex",
  color: "#000",
  fontSize: "20px",
  fontWeight: 700,
  alignItems: "center",
  justifyContent: "space-between",
};

export const modalParent = { p: "20px" };

export const addressPreviewGrid = { mb: "10px" };

export const addressPreview = { fontSize: "12px" };

export const addressDetailsStyles = {
  gridTemplateColumns: { xs: "1fr", sm: "1fr 1fr" },
  display: "grid",
  gridGap: 10,
};

export const radiusErrorStyles = {
  color: "#FF0238",
  fontSize: "12px",
  fontWeight: 600,
  pb: "5px",
  position: "absolute",
  top: "-4px",
  left: "0px",
};

export const deliveryDetailsHeading = {
  fontSize: { xs: "16px", sm: "18px" },
  fontWeight: 700,
  color: "#000000",
  margin: "0px !important",
};

export const deliveryAddressStack = {
  flexDirection: "row",
  alignItems: "flex-start",
  justifyContent: "space-between",
};

export const editActionStyle = {
  fontSize: "14px",
  fontWeight: 600,
  color: "#35A1DE",
  cursor: "pointer",
  borderBottom: "1px solid",
  height: "fit-content",
};

export const deliveryAddress = {
  fontSize: "14px",
  fontWeight: 300,
  color: "#000",
  mt: "10px !important",
};

export const deliveryAddressGrid = {
  display: "grid",
  gridTemplateColumns: "1fr",
};

export const enterAddressStyles = {
  padding: "13px 10px",
  fontSize: " 12px",
  fontWeight: 400,
  borderRadius: "6px",
  border: "1px solid rgba(0, 0, 0, 0.12)",
  background: "#FFF",
  width: "100%",
  color: "rgba(0, 0, 0, 0.50)",
  lineHeight: " 20px",
  textTransform: "unset",
  textAlign: "left",
  cursor: "pointer",
};

export const summaryButtonStyles = {
  borderRadius: "0 6px 6px 0",
  padding: "12px 14px",
  fontSize: "12px",
  fontWeight: 400,
  lineHeight: "15px",
  width: "fit-content",
};

export const labelStyles = {
  fontSize: "14px",
  fontWeight: 500,
  color: "#000000",
};

export const stripeField = {
  border: "1px solid",
  borderColor: "#0000001F",
  p: "13px 10px",
  borderRadius: "6px",
};

export const stripeForm = {
  style: {
    base: {
      color: "#303238",
      fontSize: "14px",
      fontWeight: 400,

      "::placeholder": {
        color: "#949393",
        fontWeight: 400,
        fontSize: "14px",
      },
    },
    invalid: { color: "#9e2146" },
  },
  hidePostalCode: true,
};

export const mapContainer = {
  width: "100%",
  height: "370px",
  borderRadius: "6px",
  zIndex: "1",
};

export const mapContainerBox = { position: "relative", ...mapContainer };

export const locationIcon = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  fontSize: "50px !important",
  color: "#FF0238",
  zIndex: "9999",
};

export const dividerStyle = {
  borderColor,
  m: "20px 0px !important",
};

export const couponTipField = { ...inputStyles, borderRadius: "6px 0 0 6px" };

export const couponFieldBox = { width: "100%" };

export const pickupHeading = {
  fontSize: { xs: "16px", sm: "18px" },
  fontWeight: 700,
  color: "#000",
  margin: "0px !important",
};

export const pickupStackStyles = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
};

export const paymentLoaderStyles = { color: "#000", ml: "10px" };

export const paymentLabelStack = {
  display: "flex",
  flexDirection: "row",
  alignItems: "center",
};

export const subscriptionCardStyles = {
  display: "flex",
  justifyContent: "center",
  whiteSpace: "nowrap",
  width: "70px",
  p: "18px 0px",

  fontSize: "14px",
  fontWeight: 400,
  lineHeight: "20px",
};

// margin added for responsive view in case of more than two radio
export const subscriptionTypeLabel = { mt: "10px" };

export const paymentCardsStyles = {
  ...justifySpaceBetween,

  p: "10px",
  borderRadius,
  border: "1px solid",
  cursor: "pointer",
};

export const addCardBtnStyles = {
  ...labelStyles,

  justifyContent: "start",
  backgroundColor: "none",
  p: "0 3px !important",
  width: "fit-content",

  "&:hover": { backgroundColor: "unset" },
};

export const paymentImageStyle = { width: "24px" };

export const saveCardChkBox = {
  color: "rgba(0, 0, 0, 0.25)",

  "&.Mui-checked": { color: "#000" },
  "&.Mui-disabled": { color: "rgba(0, 0, 0, 0.38)" },
  "& .MuiSvgIcon-root": {
    borderRadius: "3px",
    backgroundColor: "rgba(250, 250, 250, 1)",
  },
};

export const saveCardLabel = {
  m: "0",
  color: "#000",

  "& .MuiTypography-root": { fontSize: "14px" },
};

export const stripeFormStyles = {
  border: "1px solid #E4E4E4",
  borderRadius,
  p: "20px",
};

export const paymentCardsIcons = {
  width: { xs: "40px", sm: "50px" },
  objectFit: "contain",

  ":nth-of-type(2)": { width: { xs: "26px", sm: "30px" } },
};

export const cardsTabStyle = {
  ...buttonLoaderStyles,

  display: "block !important",
};

export const subscriptionCardBox = {
  display: "flex",
  alignItems: "center",
  position: "relative",
};

export const cardScrollStyle = {
  position: "absolute",
  background: "#D9D9D9",
  width: "18px",
  height: "18px",
  borderRadius: "50%",
  zIndex: 1,
};

export const cardRightScrollStyle = {
  ...cardScrollStyle,

  right: "0px",
};

export const subscriptionCards = {
  display: "flex",
  overflow: "auto",
  flexWrap: "nowrap",
  width: "100%",
  scrollbarWidth: "none",
  "&::-webkit-scrollbar": { display: "none" },
};

export const subscriptionCardStyle = {
  ...justifyAlignCenter,

  p: "20px",
  borderRadius,
  border: "1px solid",
  position: "relative",
  flex: "0 0 32%",
  cursor: "pointer",
  mr: "10px",
  ":last-child": { mr: "0px" },
};

export const scheduleDetailsBox = {
  ...justifySpaceBetween,

  alignItems: "start",
};

export const noScheduleIcon = { fontSize: "40px !important", mb: "10px" };

export const modalParentStack = {
  maxHeight: "90vh",
  minHeight: "50vh",
  justifyContent: "space-between",
};

export const modalHeaderBox = {
  ...justifySpaceBetween,
  ...stickyContent,

  top: "0px",
};

export const modalHeadingStyle = {
  fontSize: "20px",
  fontWeight: 700,
  color: "#000",
};

export const modalContentStack = { p: "1px 28px" };

export const modalFooterBox = {
  ...stickyContent,

  bottom: "0px",
};

export const noScheduleMessage = {
  ...deliveryAddress,

  fontSize: "12px",
  color: "#585858",
};

export const formBtnMobile = {
  display: { xs: "block", md: "none" },
  p: "0 10px 10px",
};

export const formBtnWeb = {
  display: { xs: "none", md: "block" },
  p: "0 0 10px",
};

export const formButtonParent = {
  position: "sticky",
  bottom: 0,
  backdropFilter: "blur(10px)",
};

export const tooltipStyles = { ...deliveryAddress, mt: 0 };

export const bankParentNote = {
  ...infoMessageStyle,

  p: "10px",
  background: "#E9F7FF",
  borderRadius,
};

export const bankNameStyle = { ...infoMessageStyle, color: "#********" };

export const accountInfoStack = {
  p: "10px",
  background: "#F9F9F9",
  borderRadius,
};
