import { useEffect, useState, useRef } from "react";

import {
  Typo<PERSON>,
  Stack,
  FormControlLabel,
  Radio,
  RadioGroup,
  FormControl,
  Box,
  CircularProgress,
  Tabs,
  Tab,
} from "@mui/material";
import { Close, EventBusyTwoTone } from "@mui/icons-material";
import moment from "moment";

import { useGetPickupHours } from "customHooks/useGetPickupHours";

import Modal from "components/modal";
import Button from "components/button";

import {
  cardsTabStyle,
  labelStyles,
  modalContentStack,
  modalFooterBox,
  modalHeaderBox,
  modalHeadingStyle,
  modalParentStack,
  noScheduleIcon,
  pickupDatesTabs,
  pickupTabTypoStyle,
  tabsScrollStyles,
  timeSlotStyles,
} from "checkout/checkoutForm/index.styles";
import { pointer } from "styles";
import { OrderScheduleType } from "types";
import { OrderScheduleModalProps } from "checkout/index.types";
import { dateTimeFormats, orderTypes } from "utils/constant";
import {
  compareCurrentTime,
  convertTimeZoneOffset,
  formatDay,
  generateScheduleDates,
  validateOrderSchedule,
} from "utils/helperFunctions";
import { dispatch, useSelector } from "store";
import { bulkUpdateCheckoutStore } from "store/slices/checkout";

const UpdateOrderSchedule: React.FC<OrderScheduleModalProps> = ({
  toggleUpdateSchedule,
  handleToggleUpdateSchedule,
}) => {
  const [datesList, setDatesList] = useState<string[]>([]);
  const [timeSlotsList, setTimeSlotsList] = useState<string[]>([]);

  const checkoutDetails = useSelector((state) => state.checkout);

  // creating local state to avoid data reflection on main page
  const [orderSchedule, setOrderSchedule] = useState<OrderScheduleType>(
    checkoutDetails?.orderSchedule
  );

  const { branchDetails } = useSelector((state) => state.business);

  // ref to prevent get time slots API call on initial mount
  const preventPickupSlotsAPI = useRef<boolean>(true);

  // ==============================|| custom hooks ||============================== //

  // custom hook to call get time slots API
  const { loading: getPickupHoursLoading, getPickupHoursAPICall } =
    useGetPickupHours();

  // ==============================|| useEffect hook ||============================== //

  useEffect(() => {
    // function call to generate dates for order schedule
    getOrderScheduleDates();

    // populate the current date with respect to timezone if order not scheduled or schedule is outdated
    validateScheduleDate();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    // skip the API call on initial mount to prevent uncontrolled loop because of validateScheduleDate function
    if (preventPickupSlotsAPI.current) {
      preventPickupSlotsAPI.current = false;

      return;
    }

    // function to call API to get time slots for selected date
    if (orderSchedule?.date) {
      callGetPickupHoursAPI();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderSchedule?.date]);

  // ==============================|| handler functions ||============================== //

  // function to call get pickup hours API
  const callGetPickupHoursAPI = async (): Promise<void> => {
    // get pickup hours API call
    const result: string[] = await getPickupHoursAPICall({
      params: {
        bid: checkoutDetails?.branchId,
        date: orderSchedule?.date,
        type: orderTypes?.PICK_UP,
      },
    });

    // update state
    setTimeSlotsList(result);

    // set the initial time slot if not exists
    if (!orderSchedule?.time) {
      const updatedSchedule: OrderScheduleType = {
        ...orderSchedule,
        time: result?.[0] || "",
      };

      // update the local state to reflect in modal
      setOrderSchedule(updatedSchedule);

      // save schedule in store to reflect on front page if modal is not open (to auto select in pickup now case)
      if (!toggleUpdateSchedule) {
        // function call to save schedule in store
        updateScheduleInStore(updatedSchedule);
      }
    }
  };

  // function to generate dates for order schedule
  const getOrderScheduleDates = (): void => {
    // get 7 days list with respect to provided timeZone
    const datesList: string[] = generateScheduleDates(branchDetails?.timeZone);

    // update state
    setDatesList(datesList);
  };

  // populate the current date with respect to timezone if order not scheduled or schedule is outdated
  const validateScheduleDate = (): void => {
    /**
     * if => reset to current date
     * => !orderSchedule?.date => true if date is falsy value
     * => !compareCurrentTime(orderSchedule, branchDetails?.timeZone) => true if schedule is before current date & time
     * else
     * => call API to get time slots for selected date to populate modal
     */
    if (
      !orderSchedule?.date ||
      !compareCurrentTime(orderSchedule, branchDetails?.timeZone)
    ) {
      // update local state with current date
      setOrderSchedule({
        date: convertTimeZoneOffset(branchDetails?.timeZone),
        time: "",
      });
    } else {
      // function to call API to get time slots for selected date to populate modal
      callGetPickupHoursAPI();
    }
  };

  // function to handle the time slot selection
  const handleTimeSlotChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    // update state
    setOrderSchedule({
      ...orderSchedule,
      time: event?.target?.value || "",
    });
  };

  // function to handle the date change
  const handleDateChange = (
    event: React.SyntheticEvent,
    date: string
  ): void => {
    // update state
    setOrderSchedule({ date, time: "" });
  };

  // function to handle the save schedule button
  const handleSaveSchedule = async (): Promise<void> => {
    // function call to save schedule in store
    updateScheduleInStore(orderSchedule);

    // close the model
    handleToggleUpdateSchedule();
  };

  // function to save schedule in store
  const updateScheduleInStore = async (
    orderSchedule: OrderScheduleType
  ): Promise<void> => {
    // update order schedule & remove validation error message (may exist) in store
    dispatch(
      bulkUpdateCheckoutStore({
        type: "",
        value: {
          orderSchedule,
          validationError: {
            ...checkoutDetails?.validationError,
            orderSchedule: false,
          },
        },
      })
    );
  };

  // ==============================|| UI ||============================== //

  // function to return the date UI in tabs
  const getDateUI = (date: string): JSX.Element => {
    return (
      <>
        <Typography>{formatDay(date, branchDetails?.timeZone)}</Typography>

        <Typography>
          {moment(date)?.format(dateTimeFormats?.DAY_MONTH)}
        </Typography>
      </>
    );
  };

  // function to return the date tabs labels
  const getScheduleDayLabel = (date: string): JSX.Element => {
    return (
      <>
        {/* display loader only on API call & selected schedule */}
        {getPickupHoursLoading && orderSchedule?.date === date && (
          <CircularProgress title="Loader" size={24} sx={cardsTabStyle} />
        )}

        {/* get the date UI */}
        {getDateUI(date)}
      </>
    );
  };

  // function to return the time slots
  const getScheduleHours = (): JSX.Element => {
    return timeSlotsList?.length ? (
      <FormControl>
        <RadioGroup
          aria-label="dynamic-radio-group"
          name="dynamic-radio-group"
          value={orderSchedule?.time}
          onChange={handleTimeSlotChange}
          sx={timeSlotStyles}
        >
          {timeSlotsList?.map((slot) => (
            <FormControlLabel
              key={slot}
              value={slot}
              control={<Radio />}
              label={slot}
              labelPlacement="start"
            />
          ))}
        </RadioGroup>
      </FormControl>
    ) : (
      <>
        {!getPickupHoursLoading && (
          <Stack sx={{ alignItems: "center" }}>
            <EventBusyTwoTone sx={noScheduleIcon} />

            <Typography sx={labelStyles}>
              Oops! The slot is not available. Select another
            </Typography>
            <Typography sx={labelStyles}>day/date</Typography>
          </Stack>
        )}
      </>
    );
  };

  return (
    <Modal open={toggleUpdateSchedule} onClose={handleToggleUpdateSchedule}>
      <Stack sx={modalParentStack}>
        <Stack>
          <Box sx={modalHeaderBox}>
            <Typography sx={modalHeadingStyle}>
              {`Schedule ${checkoutDetails?.orderType}`}
            </Typography>

            <Close sx={pointer} onClick={handleToggleUpdateSchedule} />
          </Box>

          <Stack spacing={2.5} sx={modalContentStack}>
            <Box sx={tabsScrollStyles}>
              <Tabs
                value={orderSchedule?.date}
                onChange={handleDateChange}
                aria-label="dynamic tabs example"
                sx={{
                  ...pickupDatesTabs,

                  "& .Mui-selected": {
                    color: "#000",
                    borderColor: "#000",
                    background: getPickupHoursLoading ? "#0000001f" : "#FFFFFF",
                  },
                }}
                variant="scrollable"
                scrollButtons={true}
                allowScrollButtonsMobile
              >
                {datesList.map((date) => (
                  <Tab
                    key={date}
                    value={date}
                    className="toggletabs"
                    disabled={getPickupHoursLoading}
                    sx={pickupTabTypoStyle}
                    label={getScheduleDayLabel(date)}
                  />
                ))}
              </Tabs>
            </Box>

            {/* schedule time slots UI */}
            {getScheduleHours()}
          </Stack>
        </Stack>

        <Box sx={modalFooterBox}>
          <Button
            onClick={handleSaveSchedule}
            disabled={!validateOrderSchedule(orderSchedule)}
          >
            Save Schedule
          </Button>
        </Box>
      </Stack>
    </Modal>
  );
};

export default UpdateOrderSchedule;
