import { useCallback } from "react";

import useAxios from "axios-hooks";
import { AxiosRequestConfig } from "axios";

import { paymentMethods } from "utils/constant";
import { paymentMethodFailureMessage } from "utils/helperFunctions";
import { onlinePayment } from "utils/apiConfig";
import { dispatch, useSelector } from "store";
import { bulkUpdateCheckoutStore } from "store/slices/checkout";

export const usePayFastPayment = () => {
  const { businessId, paymentDetails, checkoutProcessState } = useSelector(
    (state) => state.checkout
  );

  // ==============================|| API ||============================== //

  // API call to get payFast token
  const [{ data, loading, error }, getPayFastTokenAPICall] = useAxios(
    onlinePayment(businessId, paymentMethods?.PAY_FAST?.value),
    { manual: true }
  );

  // ==============================|| handler functions ||============================== //

  // function to get payFast payment token
  const createPayFastToken = useCallback(
    async (config: AxiosRequestConfig, orderId: number): Promise<void> => {
      try {
        // API call to get payFast token
        const { data } = await getPayFastTokenAPICall(config);

        // verify API response
        if (data?.status === 200 && data?.result?.token) {
          // store token & order id in store to load payment method
          dispatch(
            bulkUpdateCheckoutStore({
              type: "",
              value: {
                checkoutProcessState: { ...checkoutProcessState, id: orderId },
                paymentDetails: {
                  ...paymentDetails,
                  payFast: {
                    merchantId: data?.result?.merchant_id,
                    merchantName: data?.result?.merchant_name,
                    storeId: data?.result?.store_id || "",
                    invoiceId: data?.result?.invoice_id?.toString() || "",
                    token: data?.result?.token,
                  },
                },
              },
            })
          );
        } else {
          // set the payment method failure message for user
          paymentMethodFailureMessage(orderId);
        }
      } catch (error) {
        // set the payment method failure message for user
        paymentMethodFailureMessage(orderId);
      }
    },

    // eslint-disable-next-line react-hooks/exhaustive-deps
    [getPayFastTokenAPICall]
  );

  return {
    data,
    loading,
    error,
    getPayFastTokenAPICall: createPayFastToken,
  };
};
