import { Box } from "@mui/material";
import { Google, Facebook } from "@mui/icons-material";

import { useGetSocialProvider } from "customHooks/useGetSocialProvider";

import Button from "components/button";
import {
  authIconStyles,
  googleButtonStyles,
  facebookButtonStyles,
  socialButtonsBox,
} from "checkout/manageAccount/index.styles";
import { SocialAuthProps } from "checkout/manageAccount/index.types";
import { verifySocialPlatform } from "utils/helperFunctions";

const SocialAuth = ({ socialPlatform }: SocialAuthProps) => {
  // ==============================|| custom hooks ||============================== //

  // custom hook to call get social provider API
  const { getSocialProviderLoading, getSocialProviderAPICall } =
    useGetSocialProvider();

  // ==============================|| UI ||============================== //

  return (
    <Box sx={socialButtonsBox}>
      <Button
        loading={getSocialProviderLoading}
        disabled={getSocialProviderLoading}
        onClick={() => getSocialProviderAPICall(socialPlatform)}
        sx={
          verifySocialPlatform(socialPlatform)
            ? googleButtonStyles
            : facebookButtonStyles
        }
      >
        {/* displays icon & text side by side */}
        {verifySocialPlatform(socialPlatform) ? (
          <Google sx={authIconStyles} />
        ) : (
          <Facebook sx={authIconStyles} />
        )}
        {`Continue with ${
          verifySocialPlatform(socialPlatform) ? "Google" : "Facebook"
        }`}
      </Button>
    </Box>
  );
};

export default SocialAuth;
