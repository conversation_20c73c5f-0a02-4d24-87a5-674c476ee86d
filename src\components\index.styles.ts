import { borderRadius } from "utils/constant";

export const accordion = {
  width: "100%",
  boxShadow: "unset !important",

  "& .MuiAccordionSummary-content": { m: "unset" },

  "& .MuiAccordionSummary-content.Mui-expanded": { mt: "unset" },

  "& .MuiAccordionDetails-root": { p: "unset" },

  "& .MuiButtonBase-root": { p: "unset" },

  "& .css-1bm4qzb-MuiPaper-root-MuiAccordion-root.Mui-expanded": { m: "10px" },
};

export const accordionStyles = { boxShadow: "unset", margin: "0px !important" };

export const accordionSummaryStyles = {
  borderRadius: "unset",
  boxShadow: "unset",
  padding: "0px",
  minHeight: "0px !important",
  margin: "0px",

  "& .MuiAccordionSummary-content": { margin: "0px !important" },
};

export const accordionDetailsStyles = { padding: "20px 0px 0px 0px" };

export const modalBoxParentStyles = {
  "& .MuiBackdrop-root.MuiModal-backdrop": { zIndex: "9999" },
};

export const modalBoxStyles = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: { xs: "100%", sm: "100%", md: "100%", lg: "100%", xl: "529px" },
  maxWidth: "529px",
  margin: "auto auto",
  overflowX: "hidden",
  maxHeight: "100%",
  Height: "100%",
  zIndex: "9999",
  background: "#fff",
  borderRadius,

  "&:focus-visible": { outline: "unset" },
};

export const buttonStyles = {
  padding: { xs: "15px 15px", sm: "15px 20px" },
  fontSize: { xs: "12px", sm: "14px" },
  fontWeight: "700",
  lineHeight: "18px",
  width: "100%",
  boxShadow: "none",
};

export const buttonLoaderStyles = {
  color: "#000",
  position: "absolute",
  left: "50%",
  top: "50%",
  marginLeft: "-12px",
  marginTop: "-12px",
};

export const autocompleteStyles = {
  width: "100%",

  "& div.MuiOutlinedInput-multiline": { fontSize: 13 },

  "& label:not(.Mui-focused)": {
    color: "#bababa",
    fontSize: 12,
    fontWeight: "400",
  },

  "& label.MuiInputLabel-outlined": {
    transform: `translate(14px, 12px) scale(1)`,
  },
  "& label.MuiInputLabel-outlined.MuiInputLabel-shrink": {
    transform: `translate(14px, -6px) scale(0.68)`,
  },

  "& .MuiOutlinedInput-multiline": { padding: "8px 8px" },

  "& label.Mui-focused": {
    color: "#bababa",
    fontWeight: "400",
    fontSize: 16,
  },
  "& label.MuiFormLabel-filled": {
    color: "#bababa",
    fontWeight: "400",
    fontSize: 16,
  },
  "& p.MuiFormHelperText-root": { marginLeft: 0 },

  "& .MuiInput-underline:after": { borderBottomColor: "green" },

  "& .MuiFormControl-root": { width: "100%" },

  "& .MuiOutlinedInput-root": {
    pt: "13px",

    "& fieldset": { borderColor: "#0000001F", borderWidth: 0.8 },

    "&:hover fieldset": { borderColor: "#0000001F" },

    "&.Mui-focused fieldset": { borderWidth: 0.8, borderColor: "#0000001F" },

    "&.MuiOutlinedInput-root input": {
      color: "#000",
      fontSize: 12,
      fontWeight: "400",
    },

    "&.MuiOutlinedInput-root .MuiTypography-root": {
      color: "#000",
      fontSize: 12,
      fontWeight: "400",
    },

    "& .MuiOutlinedInput-input": { padding: "2px !important" },
  },
};

export const autocompleteErrorStyles = {
  position: "absolute",
  bottom: "14px",
  right: "34px",
  color: "red",
  fontSize: "12px",
};

export const autocompleteParentStyle = { position: "relative", width: "100%" };

export const timePickerStyles = {
  width: { xs: "100%", md: "50%" },

  "& .MuiInputBase-input": { cursor: "pointer" },
};

export const fieldErrorMessage = {
  color: "#FF0238",
  fontSize: "10px",
  fontWeight: 600,
};

export const signupPhoneError = {
  ...fieldErrorMessage,

  position: "absolute",
  right: "14px",
  top: "15px",
};

export const tooltipIcon = { mr: 1, color: "red" };

export const viewPasswordIcon = { mr: "-8px" };
